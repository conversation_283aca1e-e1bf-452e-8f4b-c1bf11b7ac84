import { ProductForm } from "@/components/admin/products/product-form"

// Sample product data - in a real app, this would be fetched from the database
const products = [
  {
    id: "1",
    name: "Allah Calligraphy",
    description: "Beautiful acrylic calligraphy of <PERSON>'s name.",
    material: "Acrylic",
    price: 129.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 25,
    featured: true,
  },
  {
    id: "2",
    name: "Ayatul Kursi Frame",
    description: "Elegant wooden frame with Ayatul Kursi inscription.",
    material: "Wood",
    price: 149.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 15,
    featured: true,
  },
]

export default function EditProductPage({ params }: { params: { id: string } }) {
  // In a real app, this would fetch the product from the database
  const product = products.find((p) => p.id === params.id) || null

  if (!product) {
    return <div>Product not found</div>
  }

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
      <ProductForm product={product} />
    </div>
  )
}

