<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
        rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script
        src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000"
        data-border-radius="small"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
        <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16 items-center">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center"><img class="h-8 w-auto"
                            src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png" alt="Logo" /><span
                            class="ml-2 text-xl font-semibold text-gray-900">DairyPro</span></div>
                    <div class="hidden md:flex md:ml-8 space-x-6"><a href="#"
                            class="text-custom px-3 py-2 text-sm font-medium border-b-2 border-custom">Cattle</a><a
                            href="#" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Feed</a><a
                            href="#"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Labour</a><a
                            href="#"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Production</a><a
                            href="#" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Sales</a><a
                            href="#"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Reporting</a></div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-500">
                        <span id="current-time"></span>
                    </div>
                    <button class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium">
                        <i class="fas fa-plus mr-2"></i>Quick Add
                    </button>
                </div>
            </div>
        </div>
    </nav>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-900">Cattle Health Records</h1><a href="#"
                    class="text-custom hover:text-custom-dark"><i class="fas fa-arrow-left mr-2"></i>Back to List</a>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="grid grid-cols-2 gap-6">
                    <div class="col-span-2">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold">Vaccination History</h2><button
                                class="bg-custom text-white px-3 py-1 rounded-md text-sm"
                                onclick="document.getElementById(&#39;vaccination-form&#39;).style.display=&#39;flex&#39;">Add
                                Vaccination</button>
                        </div>
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left">Date</th>
                                    <th class="px-4 py-2 text-left">Vaccine</th>
                                    <th class="px-4 py-2 text-left">Administrator</th>
                                    <th class="px-4 py-2 text-left">Next Due</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b">
                                    <td class="px-4 py-2">2024-01-15</td>
                                    <td class="px-4 py-2">FMD Vaccine</td>
                                    <td class="px-4 py-2">Dr. Smith</td>
                                    <td class="px-4 py-2">2024-07-15</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="px-4 py-2">2023-12-01</td>
                                    <td class="px-4 py-2">Brucellosis</td>
                                    <td class="px-4 py-2">Dr. Johnson</td>
                                    <td class="px-4 py-2">2024-12-01</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-span-2 mt-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold">Health Records</h2><button
                                class="bg-custom text-white px-3 py-1 rounded-md text-sm">Add Record</button>
                        </div>
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left">Date</th>
                                    <th class="px-4 py-2 text-left">Condition</th>
                                    <th class="px-4 py-2 text-left">Treatment</th>
                                    <th class="px-4 py-2 text-left">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b">
                                    <td class="px-4 py-2">2024-02-01</td>
                                    <td class="px-4 py-2">Routine Check</td>
                                    <td class="px-4 py-2">General examination</td>
                                    <td class="px-4 py-2"><span
                                            class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Healthy</span>
                                    </td>
                                </tr>
                                <tr class="border-b">
                                    <td class="px-4 py-2">2024-01-20</td>
                                    <td class="px-4 py-2">Mastitis</td>
                                    <td class="px-4 py-2">Antibiotics course</td>
                                    <td class="px-4 py-2"><span
                                            class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">Recovered</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-span-2 mt-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold">Notes &amp; Observations</h2>
                        </div><textarea class="w-full border rounded-md p-3" rows="4"
                            placeholder="Add notes about the cattle" s="" health="" and="" behavior...'=""></textarea>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center"
        id="vaccination-form">
        <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">Add New Vaccination</h2><button
                    class="text-gray-500 hover:text-gray-700"><i class="fas fa-times"></i></button>
            </div>
            <form class="space-y-4">
                <div><label class="block text-sm font-medium text-gray-700">Vaccination Date</label><input type="date"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-custom focus:ring-custom" />
                </div>
                <div><label class="block text-sm font-medium text-gray-700">Vaccine Type</label><select
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-custom focus:ring-custom">
                        <option>Select Vaccine</option>
                        <option>FMD Vaccine</option>
                        <option>Brucellosis</option>
                        <option>BVD</option>
                        <option>IBR</option>
                    </select></div>
                <div><label class="block text-sm font-medium text-gray-700">Administrator</label><input type="text"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-custom focus:ring-custom"
                        placeholder="Enter administrator name" /></div>
                <div><label class="block text-sm font-medium text-gray-700">Next Due Date</label><input type="date"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-custom focus:ring-custom" />
                </div>
                <div><label class="block text-sm font-medium text-gray-700">Notes</label><textarea
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-custom focus:ring-custom"
                        rows="3" placeholder="Add any additional notes..."></textarea></div>
                <div class="flex justify-end space-x-3 mt-6"><button type="button"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button><button
                        type="submit"
                        class="px-4 py-2 bg-custom text-white rounded-md text-sm font-medium hover:bg-custom-dark">Save
                        Vaccination</button></div>
            </form>
        </div>
    </div>
    <footer class="bg-white border-t border-gray-200 mt-8">
        <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    Today&#39;s Transactions: 45 | Pending Orders: 12
                </div>
                <div class="flex space-x-4">
                    <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">Reports</button>
                    <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">Settings</button>
                    <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">Support</button>
                </div>
            </div>
        </div>
    </footer>
    <script>
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        setInterval(updateTime, 1000);
        updateTime();
        const salesChart = echarts.init(document.getElementById('sales-chart'));
        const option = {
            animation: false,
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: [820, 932, 901, 934, 1290, 1330, 1320],
                type: 'line',
                smooth: true,
                lineStyle: {
                    color: '#4F46E5'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: 'rgba(79, 70, 229, 0.3)'
                    }, {
                        offset: 1,
                        color: 'rgba(79, 70, 229, 0.1)'
                    }])
                }
            }]
        };
        salesChart.setOption(option);
        window.addEventListener('resize', function () {
            salesChart.resize();
        });
    </script>
</body>

</html>