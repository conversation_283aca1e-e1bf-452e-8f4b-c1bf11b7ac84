<?php
include('config.php');
session_start();

if (isset($_POST['add_to_favorites']) && isset($_POST['product_id'])) {
    $product_id = (int)$_POST['product_id'];
    $user_id = $_SESSION['user_id'] ?? 0;

    if ($user_id > 0) {
        $stmt = $pdo->prepare("INSERT INTO favorites (user_id, product_id) VALUES (?, ?) ON DUPLICATE KEY UPDATE product_id = product_id");
        $stmt->execute([$user_id, $product_id]);

        header('Location: products.php');
        exit;
    } else {
        // Redirect to login page if not logged in
        header('Location: login.php');
        exit;
    }
}
?>
