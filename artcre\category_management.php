<?php
session_start();
require_once 'db.php'; // Make sure this includes your PDO connection

// Handle form submission to add a category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['category_name'])) {
    $categoryName = trim($_POST['category_name']);
    if (!empty($categoryName)) {
        $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (:name)");
        $stmt->execute([':name' => $categoryName]);
        header("Location: category_management.php");
        exit;
    }
}

// Handle category update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_category_id'])) {
    $categoryId = $_POST['update_category_id'];
    $newCategoryName = trim($_POST['new_category_name']);
    if (!empty($newCategoryName)) {
        $stmt = $pdo->prepare("UPDATE categories SET name = :name WHERE id = :id");
        $stmt->execute([':name' => $newCategoryName, ':id' => $categoryId]);
        header("Location: category_management.php");
        exit;
    }
}

// Fetch categories from the database
$stmt = $pdo->query("SELECT * FROM categories");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Category Management</title>
    
    <style>
        .edit-category-form { display: none; }
    </style>
    <link rel="stylesheet" href="user_manage_style.css"> <!-- Include your CSS file -->
    <link rel="stylesheet" href="sidebar.css">
    <script>
        function showEditForm(categoryId, categoryName) {
            document.getElementById('edit-category-id').value = categoryId;
            document.getElementById('edit-category-name').value = categoryName;
            document.getElementById('edit-category-form').style.display = 'block';
        }
    </script>
</head>
<body>


<?php include 'admin_sidebar.php'; ?>
<div class="container">
    
    <h2>Category Management</h2>

    <!-- Add Category Form -->
    <form class="add-category-form" action="category_management.php" method="POST">
        <label for="category_name">New Category:</label>
        <input type="text" id="category_name" name="category_name" required>
        <button type="submit">Add Category</button>
    </form>

    <!-- Edit Category Form -->
    <form class="edit-category-form" id="edit-category-form" action="category_management.php" method="POST">
        <input type="hidden" id="edit-category-id" name="update_category_id">
        <label for="edit-category-name">Edit Category:</label>
        <input type="text" id="edit-category-name" name="new_category_name" required>
        <button type="submit">Update Category</button>
    </form>

    <!-- Category List -->
    <table>
        <thead>
        <tr>
            <th>SN</th>
            <th>Category Name</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($categories as $index => $category): ?>
            <tr>
                <td><?php echo $index + 1; ?></td>
                <td><?php echo htmlspecialchars($category['name']); ?></td>
                <td>
                    <button onclick="showEditForm(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')">Edit</button>
                    <form action="delete_category.php" method="POST" style="display:inline;">
                        <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                        <button type="submit" onclick="return confirm('Are you sure you want to delete this category?');">Delete</button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>
</body>
</html>
