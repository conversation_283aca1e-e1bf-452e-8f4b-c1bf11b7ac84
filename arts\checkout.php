<?php
session_start();
include('config.php');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_SESSION['user_id']) && !empty($_SESSION['cart'])) {
        $userId = $_SESSION['user_id'];
        $cartItems = $_SESSION['cart'];

        // Insert order into orders table
        $stmt = $pdo->prepare("INSERT INTO orders (user_id, order_date) VALUES (?, NOW())");
        $stmt->execute([$userId]);
        $orderId = $pdo->lastInsertId();

        // Insert order items
        $orderItemsStmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");

        foreach ($cartItems as $item) {
            $orderItemsStmt->execute([$orderId, $item['id'], $item['quantity'], $item['price']]);
        }

        // Clear cart
        unset($_SESSION['cart']);

        header('Location: order_success.php');
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <!-- Your header code -->
    </header>

    <section class="checkout">
        <h2>Checkout</h2>
        <form method="post">
            <!-- Add form fields for shipping details, payment, etc. -->
            <button type="submit" class="btn">Place Order</button>
        </form>
    </section>

    <footer>
        <!-- Your footer code -->
    </footer>
</body>
</html>
