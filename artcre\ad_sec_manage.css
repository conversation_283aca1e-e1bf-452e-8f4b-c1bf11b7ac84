
    body {
        
        background-color: #f4f4f4; /* Light background for the body */
        
    }

    
    .content {
        margin-left: 220px; /* Space for sidebar */
        padding: 20px;
        
    }

    .advertise-section {
        background-color: #ffffff; /* White background for sections */
        border: 1px solid #ced4da; /* Border color */
        border-radius: 8px; /* Rounded corners */
        padding: 20px; /* Padding inside the section */
        margin-bottom: 20px; /* Space below the section */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Shadow for depth */
    }

    h2, h3 {
        color: #343a40; /* Dark gray for headings */
        margin-bottom: 10px; /* Space below headings */
    }

    form {
        display: flex;
        flex-direction: column; /* Stack form elements vertically */
        gap: 15px; /* Space between form elements */
        width: 80%;
    }

    /* Main Content Styles */
.main-content {
    margin-left: 210px;
    padding: 20px;
    
}

.main-content h1 {
    color: #2c3e50; /* Main content heading color */
    margin-bottom: 10px;
}

/* User Management Styles */
.container {
    max-width: 800px;
    margin: auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #007bff; /* Table header color */
    color: white;
}

tr:hover {
    background-color: #f1f1f1;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    background-color: #28a745; /* Button color */
    color: white;
    cursor: pointer;
}

button:hover {
    background-color: #218838; /* Button hover color */
}

.search-bar {
    margin-bottom: 20px;
}

    .active-advertisement {
        border: 1px solid #28a745; /* Green border for active ads */
        padding: 15px; /* Padding inside the ad section */
        border-radius: 4px; /* Rounded corners */
        background-color: #d4edda; /* Light green background */
    }

    img {
        max-width: 100%; /* Responsive image */
        height: auto; /* Maintain aspect ratio */
    }
    button {
        padding: 5px 10px;
        font-size: 14px;
        color: #fff;
        background-color: #007bff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    button:hover {
        background-color: #0056b3;
    }
    