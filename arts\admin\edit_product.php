<?php
// edit_product.php

include('config.php');

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];

    // Fetch product details
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$id]);
    $product = $stmt->fetch();

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $name = $_POST['name'];
        $price = $_POST['price'];
        $image = $_FILES['image']['name'];
        $params = [$name, $price];

        if ($image) {
            // Handle file upload
            move_uploaded_file($_FILES['image']['tmp_name'], "uploads/$image");
            $sql = "UPDATE products SET name = ?, price = ?, image = ? WHERE id = ?";
            $params[] = $image; // Add image to parameters
        } else {
            $sql = "UPDATE products SET name = ?, price = ? WHERE id = ?";
        }

        $params[] = $id; // Add ID to parameters
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        header('Location: products.php');
        exit;
    }
} else {
    header('Location: products.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Product</title>
    <link rel="stylesheet" href="../styles.css">
</head>
<body>
    <!-- Header Section -->
    <?php
    session_start();
    if (!isset($_SESSION['logged_in'])) {
        header('Location: login.php');
        exit;
    }

    echo '<h1>Admin Panel</h1>';
    echo '<a href="categories.php">Manage Categories</a> | ';
    echo '<a href="products.php">Manage Products</a> | ';
    echo '<a href="logout.php">Logout</a>';
    ?>

    <!-- Edit Product Form -->
    <section class="edit-product">
        <h2>Edit Product</h2>
        <form action="edit_product.php?id=<?php echo $product['id']; ?>" method="post" enctype="multipart/form-data">
            <label for="name">Product Name:</label>
            <input type="text" name="name" value="<?php echo htmlspecialchars($product['name']); ?>" required>

            <label for="price">Price:</label>
            <input type="number" name="price" value="<?php echo htmlspecialchars($product['price']); ?>" step="0.01" required>

            <label for="image">Product Image:</label>
            <input type="file" name="image">

            <button type="submit">Update Product</button>
        </form>
    </section>

    <!-- Footer Section -->
    <footer>
        <p>&copy; 2024 My E-commerce Site. All rights reserved.</p>
    </footer>

    <script src="scripts.js"></script>
</body>
</html>
