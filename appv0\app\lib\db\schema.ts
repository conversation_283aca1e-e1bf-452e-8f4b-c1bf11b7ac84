import { PrismaClient } from "@prisma/client";

declare global {
  var prisma: PrismaClient | undefined;
}

export const prisma = global.prisma || new PrismaClient();

if (process.env.NODE_ENV !== "production") {
  global.prisma = prisma;
}

// User schema
export interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  favorites: string;
  createdAt: Date;
  updatedAt: Date;
}

// Favorites schema
export interface Favorite {
  id: string;
  userId: string;
  productId: string;
  createdAt: Date;
}

// Database schema
export const schema = `
  model User {
    id        String   @id @default(uuid())
    name      String
    email     String   @unique
    password  String
    favorites String   @default("[]")
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
  }

  model Favorite {
    id        String   @id @default(uuid())
    userId    String
    productId String
    createdAt DateTime @default(now())
    user      User     @relation(fields: [userId], references: [id])
  }
`;
