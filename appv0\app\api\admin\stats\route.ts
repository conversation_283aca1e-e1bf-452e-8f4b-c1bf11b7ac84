import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get current month's start and end dates
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Get last month's start and end dates
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Fetch current month stats
    const [currentMonthStats, lastMonthStats] = await Promise.all([
      prisma.$transaction([
        prisma.order.aggregate({
          where: {
            status: "COMPLETED",
            createdAt: {
              gte: currentMonthStart,
              lte: currentMonthEnd,
            },
          },
          _sum: {
            total: true,
          },
          _count: true,
        }),
        prisma.product.count(),
        prisma.user.count(),
      ]),
      prisma.$transaction([
        prisma.order.aggregate({
          where: {
            status: "COMPLETED",
            createdAt: {
              gte: lastMonthStart,
              lte: lastMonthEnd,
            },
          },
          _sum: {
            total: true,
          },
          _count: true,
        }),
        prisma.product.count({
          where: {
            createdAt: {
              lte: lastMonthEnd,
            },
          },
        }),
        prisma.user.count({
          where: {
            createdAt: {
              lte: lastMonthEnd,
            },
          },
        }),
      ]),
    ]);

    // Calculate changes
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return 100;
      return ((current - previous) / previous) * 100;
    };

    const stats = {
      totalRevenue: currentMonthStats[0]._sum.total || 0,
      totalProducts: currentMonthStats[1],
      totalOrders: currentMonthStats[0]._count,
      totalUsers: currentMonthStats[2],
      revenueChange: calculateChange(
        currentMonthStats[0]._sum.total || 0,
        lastMonthStats[0]._sum.total || 0
      ),
      productsChange: calculateChange(currentMonthStats[1], lastMonthStats[1]),
      ordersChange: calculateChange(
        currentMonthStats[0]._count,
        lastMonthStats[0]._count
      ),
      usersChange: calculateChange(currentMonthStats[2], lastMonthStats[2]),
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("[ADMIN_STATS]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
