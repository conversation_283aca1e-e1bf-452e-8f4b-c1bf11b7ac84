/* General Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f4f4f4;
  color: #333;
}

h1,
h2,
p {
  margin-bottom: 15px;
}

a {
  text-decoration: none;
  color: inherit;
}

/* Header Styles */
header {
  background-color: #333;
  padding: 15px 0;
}

nav ul {
  list-style: none;
  display: flex;
  justify-content: center;
}

nav ul li {
  margin: 0 20px;
}

nav ul li a {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

nav ul li a:hover {
  color: #f39c12;
}

/* Hero Section */
.hero {
  background-image: url("../assets/images/banner.jpg");
  background-size: cover;
  background-position: center;
  color: #fff;
  padding: 100px 0;
  text-align: center;
}

.hero h1 {
  font-size: 50px;
  font-weight: bold;
  margin-bottom: 15px;
}

.hero p {
  font-size: 20px;
  margin-bottom: 30px;
}

.cta-button {
  background-color: #f39c12;
  color: white;
  padding: 10px 20px;
  font-size: 18px;
  border-radius: 5px;
  font-weight: bold;
}

.cta-button:hover {
  background-color: #e67e22;
}

/* Featured Products Section */
.featured-products {
  padding: 50px 0;
  text-align: center;
}

.featured-products h2 {
  font-size: 36px;
  margin-bottom: 30px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.product-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
}

.product-item img {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-item p {
  font-size: 18px;
  margin-bottom: 15px;
}

.product-item a.cta-button {
  font-size: 16px;
}

/* Footer Section */
footer {
  background-color: #333;
  color: #fff;
  text-align: center;
  padding: 20px 0;
}

footer p {
  font-size: 14px;
}

footer a {
  color: #f39c12;
}

footer a:hover {
  color: #e67e22;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr 1fr;
  }

  .hero h1 {
    font-size: 40px;
  }

  .hero p {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
  }

  .hero h1 {
    font-size: 30px;
  }

  .hero p {
    font-size: 16px;
  }
}
