<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script
      src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js"
      data-color="#000000"
      data-border-radius="small"
    ></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
                >DairyPro</span
              >
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Dashboard</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Feed</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Labour</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Production</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Sales</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Reporting</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
            <button
              class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
            >
              <i class="fas fa-plus mr-2"></i>Quick Add
            </button>
          </div>
        </div>
      </div>
    </nav>
    <div class="bg-gray-100 border-b border-gray-200" id="sub-menu-bar">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          class="flex items-center space-x-6 overflow-x-auto py-3 scrollbar-hide"
        >
          <a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Record by Cattle</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Milk</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Ghee</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Paneer</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Dahi</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Chhas</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Makhkhan</a
          ><a
            href="#"
            class="text-custom hover:text-gray-900 whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Dung</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Urine</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Bio Fertilizer</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Bio CNG</a
          >
        </div>
      </div>
    </div>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-4">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <i class="fas fa-tractor text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Production</p>
              <h3 class="text-xl font-semibold text-gray-900">1,200 kg</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <i class="fas fa-warehouse text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Stock Level</p>
              <h3 class="text-xl font-semibold text-gray-900">850 kg</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100">
              <i class="fas fa-coins text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Revenue Today</p>
              <h3 class="text-xl font-semibold text-gray-900">₹24,000</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100">
              <i class="fas fa-truck text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pending Orders</p>
              <h3 class="text-xl font-semibold text-gray-900">8</h3>
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-6 mt-6">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Create New Batch
            </h2>
            <form class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1"
                    >Batch Type</label
                  ><select
                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                  >
                    <option>Vermicompost</option>
                    <option>Organic Manure</option>
                    <option>Bio Fertilizer</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1"
                    >Quantity (kg)</label
                  ><input
                    type="number"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                    placeholder="Enter quantity"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1"
                    >Start Date</label
                  ><input
                    type="date"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1"
                    >Expected Completion</label
                  ><input
                    type="date"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                  />
                </div>
              </div>
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1"
                  >Raw Materials</label
                >
                <div class="space-y-3">
                  <div class="flex items-center gap-4">
                    <select
                      class="flex-1 border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                    >
                      <option>Cow Dung</option>
                      <option>Crop Residue</option>
                      <option>Green Waste</option>
                      <option>Earthworms</option></select
                    ><input
                      type="number"
                      placeholder="Quantity (kg)"
                      class="w-32 border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                    /><button
                      type="button"
                      class="p-2 text-gray-400 hover:text-gray-500"
                    >
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1"
                  >Notes</label
                ><textarea
                  class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
                  rows="3"
                  placeholder="Add any additional notes or instructions"
                ></textarea>
              </div>
              <div class="mt-6 flex gap-3">
                <button
                  type="submit"
                  class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
                >
                  Create Batch</button
                ><button
                  type="button"
                  class="!rounded-button border border-gray-300 text-gray-700 px-4 py-2 text-sm font-medium"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <div class="hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold text-gray-900">
                Raw Material Stock
              </h2>
            </div>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Cow Dung</span
                ><span class="text-sm font-medium text-gray-900">2,500 kg</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Crop Residue</span
                ><span class="text-sm font-medium text-gray-900">1,200 kg</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Green Waste</span
                ><span class="text-sm font-medium text-gray-900">800 kg</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Earthworms</span
                ><span class="text-sm font-medium text-gray-900">100 kg</span>
              </div>
            </div>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold text-gray-900">
                Quality Metrics
              </h2>
            </div>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Moisture Content</span
                ><span class="text-sm font-medium text-gray-900">35%</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">pH Level</span
                ><span class="text-sm font-medium text-gray-900">6.8</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">NPK Ratio</span
                ><span class="text-sm font-medium text-gray-900">2:1:1</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Organic Matter</span
                ><span class="text-sm font-medium text-gray-900">65%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Today&#39;s Transactions: 45 | Pending Orders: 12
          </div>
          <div class="flex space-x-4">
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Reports
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Settings
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Support
            </button>
          </div>
        </div>
      </div>
    </footer>
    <script>
      function updateTime() {
        const now = new Date();
        document.getElementById("current-time").textContent =
          now.toLocaleString();
      }
      setInterval(updateTime, 1000);
      updateTime();
      const salesChart = echarts.init(document.getElementById("sales-chart"));
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#4F46E5",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(79, 70, 229, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(79, 70, 229, 0.1)",
                },
              ]),
            },
          },
        ],
      };
      salesChart.setOption(option);
      window.addEventListener("resize", function () {
        salesChart.resize();
      });
    </script>
  </body>
</html>
