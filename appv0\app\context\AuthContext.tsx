"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  name: string;
  email: string;
  favorites: string[];
  isAdmin?: boolean;
}

interface AuthContextType {
  user: User | null;
  isAdmin: boolean;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (name: string, email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  toggleFavorite: (productId: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAdmin: false,
  loading: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  toggleFavorite: async () => {},
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // For demo purposes, we'll check for admin credentials
      const isAdmin = email === "<EMAIL>" && password === "admin123";

      const mockUser = {
        id: "1",
        name: isAdmin ? "Admin User" : "Regular User",
        email: email,
        favorites: [],
        isAdmin: isAdmin,
      };

      // First update the state
      setUser(mockUser);
      localStorage.setItem("user", JSON.stringify(mockUser));

      // Wait for the next tick to ensure state is updated
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Then redirect based on user role
      if (isAdmin) {
        router.push("/admin");
      } else {
        router.push("/");
      }
    } catch (error) {
      console.error("Sign in failed:", error);
      throw error;
    }
  };

  const signUp = async (name: string, email: string, password: string) => {
    try {
      const newUser = {
        id: Date.now().toString(),
        name,
        email,
        favorites: [],
        isAdmin: false,
      };
      setUser(newUser);
      localStorage.setItem("user", JSON.stringify(newUser));
      router.push("/");
    } catch (error) {
      console.error("Sign up failed:", error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      setUser(null);
      localStorage.removeItem("user");
      router.push("/signin");
    } catch (error) {
      console.error("Sign out failed:", error);
      throw error;
    }
  };

  const toggleFavorite = async (productId: string) => {
    if (!user) return;

    try {
      const updatedFavorites = user.favorites.includes(productId)
        ? user.favorites.filter((id) => id !== productId)
        : [...user.favorites, productId];

      const updatedUser = {
        ...user,
        favorites: updatedFavorites,
      };

      setUser(updatedUser);
      localStorage.setItem("user", JSON.stringify(updatedUser));
    } catch (error) {
      console.error("Toggle favorite failed:", error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAdmin: user?.isAdmin || false,
        loading,
        signIn,
        signUp,
        signOut,
        toggleFavorite,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);
