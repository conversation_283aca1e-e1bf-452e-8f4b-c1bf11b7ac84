<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
        rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script
        src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000"
        data-border-radius="small"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
        <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16 items-center">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center"><img class="h-8 w-auto"
                            src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png" alt="Logo" /><span
                            class="ml-2 text-xl font-semibold text-gray-900"><a href="../index.php">DairyPro</a></span>
                    </div>
                    <div class="hidden md:flex md:ml-8 space-x-6"><a href="../cattle/cattle_management.php"
                            class="text-custom px-3 py-2 text-sm font-medium border-b-2 border-custom">Cattle</a><a
                            href="../feed/feed_management.php"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Feed</a><a
                            href="../labour/labour_management.php"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Labour</a><a
                            href="../production/production_management.php"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Production</a><a
                            href="../sales/sales_management.php"
                            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Sales</a></div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-500">
                        <span id="current-time"></span>
                    </div>

                </div>
            </div>
        </div>
    </nav>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Add this CSS for modal styling -->
        <style>
            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                justify-content: center;
                align-items: center;
            }

            .modal-content {
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                width: 400px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .modal-header {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        </style>

        <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="max-w-4xl mx-auto">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Cattle Health Records</h1><a href="#"
                        class="text-custom hover:text-custom-dark"><i class="fas fa-arrow-left mr-2"></i>Back to
                        List</a>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="grid grid-cols-2 gap-6">
                        <!-- Vaccination Section -->
                        <div class="col-span-2">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold">Vaccination History</h2>
                                <button onclick="openModal('vaccinationModal')"
                                    class="bg-custom text-white px-3 py-1 rounded-md text-sm">Add Vaccination</button>
                            </div>
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left">Date</th>
                                        <th class="px-4 py-2 text-left">Vaccine</th>
                                        <th class="px-4 py-2 text-left">Administrator</th>
                                        <th class="px-4 py-2 text-left">Next Due</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b">
                                        <td class="px-4 py-2">2024-01-15</td>
                                        <td class="px-4 py-2">FMD Vaccine</td>
                                        <td class="px-4 py-2">Dr. Smith</td>
                                        <td class="px-4 py-2">2024-07-15</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Health Records Section -->
                        <div class="col-span-2 mt-6">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold">Health Records</h2>
                                <button onclick="openModal('healthModal')"
                                    class="bg-custom text-white px-3 py-1 rounded-md text-sm">Add Record</button>
                            </div>
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left">Date</th>
                                        <th class="px-4 py-2 text-left">Condition</th>
                                        <th class="px-4 py-2 text-left">Treatment</th>
                                        <th class="px-4 py-2 text-left">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b">
                                        <td class="px-4 py-2">2024-02-01</td>
                                        <td class="px-4 py-2">Routine Check</td>
                                        <td class="px-4 py-2">General examination</td>
                                        <td class="px-4 py-2"><span
                                                class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Healthy</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Notes & Observations -->
                        <div class="col-span-2 mt-6">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-lg font-semibold">Notes &amp; Observations</h2>
                            </div>
                            <textarea class="w-full border rounded-md p-3" rows="4"
                                placeholder="Add notes about the cattle's health and behavior..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Vaccination Modal -->
        <div id="vaccinationModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">Add Vaccination Record</div>
                <form id="vaccinationForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <input type="date" name="date" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Vaccine</label>
                        <input type="text" name="vaccine" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Administrator</label>
                        <input type="text" name="admin" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Next Due Date</label>
                        <input type="date" name="next_due" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="closeModal('vaccinationModal')"
                            class="bg-gray-400 text-white px-4 py-2 rounded-md">Cancel</button>
                        <button type="submit" class="bg-custom text-white px-4 py-2 rounded-md">Save</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Health Record Modal -->
        <div id="healthModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">Add Health Record</div>
                <form id="healthForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <input type="date" name="date" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Condition</label>
                        <input type="text" name="condition" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Treatment</label>
                        <input type="text" name="treatment" class="w-full border rounded-md p-2" required />
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" class="w-full border rounded-md p-2" required>
                            <option value="Healthy">Healthy</option>
                            <option value="Recovered">Recovered</option>
                            <option value="Under Treatment">Under Treatment</option>
                        </select>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="closeModal('healthModal')"
                            class="bg-gray-400 text-white px-4 py-2 rounded-md">Cancel</button>
                        <button type="submit" class="bg-custom text-white px-4 py-2 rounded-md">Save</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modal Script -->
        <script>
            function openModal(modalId) {
                document.getElementById(modalId).style.display = 'flex';
            }

            function closeModal(modalId) {
                document.getElementById(modalId).style.display = 'none';
            }

            // Form Submission for Vaccination
            document.getElementById('vaccinationForm').addEventListener('submit', function (event) {
                event.preventDefault();
                alert('Vaccination record added successfully!');
                closeModal('vaccinationModal');
            });

            // Form Submission for Health Record
            document.getElementById('healthForm').addEventListener('submit', function (event) {
                event.preventDefault();
                alert('Health record added successfully!');
                closeModal('healthModal');
            });
        </script>

    </main>
    <footer class="bg-white border-t border-gray-200 mt-8">
        <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    Today&#39;s Transactions: 45 | Pending Orders: 12
                </div>
                <div class="flex space-x-4">
                    <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">Reports</button>
                    <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">Settings</button>
                    <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">Support</button>
                </div>
            </div>
        </div>
    </footer>
    <script>
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        setInterval(updateTime, 1000);
        updateTime();
        const salesChart = echarts.init(document.getElementById('sales-chart'));
        const option = {
            animation: false,
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: [820, 932, 901, 934, 1290, 1330, 1320],
                type: 'line',
                smooth: true,
                lineStyle: {
                    color: '#4F46E5'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: 'rgba(79, 70, 229, 0.3)'
                    }, {
                        offset: 1,
                        color: 'rgba(79, 70, 229, 0.1)'
                    }])
                }
            }]
        };
        salesChart.setOption(option);
        window.addEventListener('resize', function () {
            salesChart.resize();
        });
    </script>


</body>

</html>