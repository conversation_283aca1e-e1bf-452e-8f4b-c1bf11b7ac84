import Link from "next/link"
import { PlusCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ProductsTable } from "@/components/admin/products/products-table"

export default function ProductsPage() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Products</h1>
        <Link href="/admin/products/new">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Product
          </Button>
        </Link>
      </div>
      <ProductsTable />
    </div>
  )
}

