<?php
// products.php

include('config.php');


// Fetch all products
$stmt = $pdo->prepare("SELECT * FROM products");
$stmt->execute();
$products = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Royal Arts</title>
    <link rel="stylesheet" href="styles.css">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.15.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
	<style>
	@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500&display=swap');
    @import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css");
	</style>
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <h1>Royal Arts</h1>
        </div>
        <nav class="container add" id="container">
        

        <div class="navbar">
            <ul>
                <li><a href="index.php"><i class="bi bi-house"></i><span> Home</span></a></li>
				<?php
					$whatsapp_number = "+918827634236";
					$whatsapp_message = "Hello! I'm contacting you from your website.";
					$whatsapp_url = "https://wa.me/" . preg_replace('/[^0-9]/', '', $whatsapp_number) . "?text=" . urlencode($whatsapp_message);
				?>
                <li><a href="<?php echo $whatsapp_url; ?>" target="_blank"><i class="bi bi-whatsapp"></i><span>  Message Us</span></a></li>
                <li><a href="cart.php"><i class="bi bi-cart"></i><span> Cart</span></a></li>                
				<?php if (isset($_SESSION['user_id'])): ?>
                    <li><a href="favourites.php"><i class="bi bi-heart"></i><span> Favourites</span></a></li>
					<li><a href="profile.php"><i class="bi bi-person"></i><span> My Profile</span></a></li>  
					<li><a href="logout.php"><i class="bi bi-box-arrow-in-right"></i><span> Log Out</span></a></li>					
                <?php else: ?>
					<li><a href="login.php"><i class="bi bi-key"></i><span> Login</span></a></li>
                    <li><a href="register.php"><i class="bi bi-pen"></i><span> Register</span></a></li> 
                <?php endif; ?>                               
                
            </ul>
        </div>

        
    </nav>
    </header>

    <!-- Products Section -->
    <section class="products">
        <h2>Our Products</h2>
        <div class="product-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-item">
                    <img src="admin/uploads/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                    <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                    <p>Rs <?php echo number_format($product['price'], 2); ?></p>
                    <a href="product_detail.php?id=<?php echo $product['id']; ?>" class="view-details">View Details</a>
                    
                    <!-- Add to Cart Form -->
                    <form action="add_to_cart.php" method="POST">
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                        <button type="submit" name="add_to_cart">Add to Cart</button>
                    </form>

                    <!-- Add to Favorites Form -->
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <form action="add_to_favorites.php" method="POST">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <button type="submit" name="add_to_favorites">Mark as Favorite</button>
                        </form>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- Footer Section -->
    <footer>
        <p>&copy; 2024 Royal Arts. All rights reserved.</p>
    </footer>

    <script src="scripts.js"></script>
</body>
</html>
