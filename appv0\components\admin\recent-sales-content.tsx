"use client";

import { useEffect, useState } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";

interface Sale {
  id: string;
  total: number;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  product: {
    name: string;
  };
}

export default function RecentSalesContent() {
  const [state, setState] = useState<{
    sales: Sale[];
    loading: boolean;
    error: string | null;
  }>({
    sales: [],
    loading: true,
    error: null,
  });

  useEffect(() => {
    const controller = new AbortController();

    async function fetchSales() {
      try {
        const response = await fetch("/api/admin/recent-sales", {
          signal: controller.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setState((prev) => ({ ...prev, sales: data, loading: false }));
      } catch (err) {
        if (err instanceof Error && err.name !== "AbortError") {
          console.error("Failed to fetch recent sales:", err);
          setState((prev) => ({
            ...prev,
            error: "Failed to load recent sales",
            loading: false,
          }));
          toast.error("Failed to load recent sales");
        }
      }
    }

    fetchSales();

    return () => {
      controller.abort();
    };
  }, []);

  if (state.error) {
    return (
      <div className="flex items-center justify-center h-32 text-red-500">
        {state.error}
      </div>
    );
  }

  if (state.loading) {
    return null; // Loading state is handled by the parent component
  }

  if (state.sales.length === 0) {
    return (
      <div className="flex items-center justify-center h-32 text-muted-foreground">
        No recent sales found
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {state.sales.map((sale) => (
        <div key={sale.id} className="flex items-center">
          <Avatar className="h-9 w-9">
            <AvatarFallback>
              {sale.user?.name
                ? sale.user.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                : "?"}
            </AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">
              {sale.user?.name || "Unknown User"}
            </p>
            <p className="text-sm text-muted-foreground">
              {sale.product?.name || "Unknown Product"} - $
              {sale.total.toLocaleString()}
            </p>
          </div>
          <div className="ml-auto font-medium">
            {new Date(sale.createdAt).toLocaleDateString()}
          </div>
        </div>
      ))}
    </div>
  );
}
