<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script
      src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js"
      data-color="#000000"
      data-border-radius="small"
    ></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
                >Attendance History</span
              >
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Salaried Workers</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Daily Workers</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Attendance</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Settings</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
            <button
              class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
            >
              <i class="fas fa-user-plus mr-2"></i>Add User
            </button>
          </div>
        </div>
      </div>
    </nav>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-4">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-custom bg-opacity-10">
              <i class="fas fa-users text-custom"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Workers</p>
              <h3 class="text-xl font-semibold text-gray-900">156</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <i class="fas fa-user-check text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Salaried Workers</p>
              <h3 class="text-xl font-semibold text-gray-900">142</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100">
              <i class="fas fa-user-shield text-red-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Daily Workers</p>
              <h3 class="text-xl font-semibold text-gray-900">8</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100">
              <i class="fas fa-chart-pie text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Attendance Rate</p>
              <h3 class="text-xl font-semibold text-gray-900">28.5%</h3>
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mt-6">
        <div class="bg-white p-6 rounded-lg shadow w-full">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-semibold text-gray-900">
              Attendance Records
            </h2>
            <div class="flex space-x-4">
              <button
                class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
              >
                Export CSV</button
              ><button
                class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
              >
                Filter
              </button>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Employee
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Date
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Check In
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Check Out
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Duration
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm font-medium text-gray-900">
                        John Smith
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    2024-02-20
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    09:00 AM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    06:00 PM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    9h 0m
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                      >Present</span
                    >
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm font-medium text-gray-900">
                        Sarah Johnson
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    2024-02-20
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    08:45 AM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    05:30 PM
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    8h 45m
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                      >Present</span
                    >
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm font-medium text-gray-900">
                        Michael Brown
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    2024-02-20
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800"
                      >Absent</span
                    >
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="mt-4 flex items-center justify-between">
            <div class="text-sm text-gray-500">
              Showing 1 to 3 of 156 entries
            </div>
            <div class="flex justify-center mt-4">
              <nav
                class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
              >
                <a
                  href="#"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  >Previous</a
                ><a
                  href="#"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >1</a
                ><a
                  href="#"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >2</a
                ><a
                  href="#"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >3</a
                ><a
                  href="#"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  >Next</a
                >
              </nav>
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 mt-6 hidden">
        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-900">
              Attendance History
            </h2>
            <button
              class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
            >
              View All
            </button>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Date
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Worker Type
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Amount
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    2024-02-20
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Salaried Worker
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $2,450
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                    >
                      Paid
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    2024-02-19
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Daily Worker
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $5,800
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800"
                    >
                      Pending
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    2024-02-18
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Salaried Worker
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $780
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                    >
                      Paid
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">
            Worker Updates
          </h2>
          <div class="space-y-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <span
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-red-100"
                >
                  <i class="fas fa-exclamation-triangle text-red-600"></i>
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">Low stock alert</p>
                <p class="text-sm text-gray-500">
                  Cheese inventory below threshold
                </p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <span
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-yellow-100"
                >
                  <i class="fas fa-clock text-yellow-600"></i>
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">Expiring soon</p>
                <p class="text-sm text-gray-500">
                  Yogurt batch expires in 3 days
                </p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <span
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-green-100"
                >
                  <i class="fas fa-check text-green-600"></i>
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">Order completed</p>
                <p class="text-sm text-gray-500">Bulk order #12345 delivered</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Active Sessions: 45 | Last Backup: 2 hours ago
          </div>
          <div class="flex space-x-4">
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Reports
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Settings
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Support
            </button>
          </div>
        </div>
      </div>
    </footer>
    <script>
      function updateTime() {
        const now = new Date();
        document.getElementById("current-time").textContent =
          now.toLocaleString();
      }
      setInterval(updateTime, 1000);
      updateTime();
      const salesChart = echarts.init(document.getElementById("sales-chart"));
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#4F46E5",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(79, 70, 229, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(79, 70, 229, 0.1)",
                },
              ]),
            },
          },
        ],
      };
      salesChart.setOption(option);
      window.addEventListener("resize", function () {
        salesChart.resize();
      });
    </script>
  </body>
</html>
