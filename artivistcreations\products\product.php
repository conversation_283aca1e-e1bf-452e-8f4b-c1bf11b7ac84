<?php include('../includes/header.php'); ?>

<main>
    <?php
        $product_id = $_GET['id'];  // Get product ID from URL
        // Fetch product details from the database (using the ID)
        // Here we'll use a static example:
        $product = ['id' => $product_id, 'name' => 'Acrylic Wall Art', 'price' => 199, 'description' => 'Beautiful wall art made from premium acrylic.', 'image' => 'product1.jpg'];
    ?>
    
    <section class="product-details">
        <h2><?php echo $product['name']; ?></h2>
        <img src="../assets/images/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>">
        <p><?php echo $product['description']; ?></p>
        <p><strong>Price:</strong> $<?php echo $product['price']; ?></p>
        <form action="cart.php" method="POST">
            <button type="submit" class="cta-button">Add to Cart</button>
        </form>
    </section>
</main>

<?php include('../includes/footer.php'); ?>
