import React, { useState } from "react";

// MicroWorksApp - single-file React component (Tailwind CSS assumed)
// Purpose: interactive front-end prototype for a micro-tasks marketplace
// Notes for developer:
//  - Tailwind classes are used. Ensure Tailwind is enabled in the project.
//  - Replace placeholder payment handlers with real integration (Razorpay / PayPal / Stripe).
//  - Implement backend endpoints for orders, user auth, KYC, anti-fraud and delivery reporting.
//  - This demo intentionally does NOT include any automation instructions that would violate platform ToS.

const SERVICES = [
  { id: "caption_writing", title: "Caption Writing & Hashtag Research", unitLabel: "per post", baseINR: 25, baseUSD: 0.5, note: "Creative captions with strategic hashtag research" },
  { id: "graphic_design", title: "Graphic Design (Social Posts, Thumbnails)", unitLabel: "per design", baseINR: 75, baseUSD: 1.5 },
  { id: "transcription", title: "Transcription Services", unitLabel: "per minute", baseINR: 15, baseUSD: 0.3 },
  { id: "translation", title: "Translation Services", unitLabel: "per 100 words", baseINR: 50, baseUSD: 1 },
  { id: "video_editing", title: "Short Video Editing", unitLabel: "per video", baseINR: 150, baseUSD: 3 },
  { id: "voiceover", title: "Professional Voiceovers", unitLabel: "per clip", baseINR: 100, baseUSD: 2 },
  { id: "data_entry", title: "Data Entry & Excel Formatting", unitLabel: "per 100 rows", baseINR: 30, baseUSD: 0.6 },
  { id: "research_tasks", title: "Research Tasks (Leads, Info Collection)", unitLabel: "per task", baseINR: 80, baseUSD: 1.5 },
  { id: "profile_audit", title: "Profile Audits & Growth Strategies", unitLabel: "per audit", baseINR: 200, baseUSD: 4 },
  { id: "content_scheduling", title: "Social Media Content Scheduling", unitLabel: "per week", baseINR: 120, baseUSD: 2.5, note: "With proper authorization and permissions" }
];

export default function MicroWorksApp() {
  const [currency, setCurrency] = useState("INR");
  const [country, setCountry] = useState("India");
  const [cart, setCart] = useState([]);
  const [selectedService, setSelectedService] = useState(null);
  const [orderModalOpen, setOrderModalOpen] = useState(false);
  const [tosOpen, setTosOpen] = useState(false);

  function priceFor(service, quantity) {
    // Basic price calc: base * quantity. If client outside India and user wants USD pricing,
    // use baseUSD; otherwise INR. Quantity is expected in units described by the service.
    if (currency === "USD") return (service.baseUSD * quantity).toFixed(2);
    return (service.baseINR * quantity).toFixed(0);
  }

  function addToCart(service, quantity, extra = {}) {
    const item = { ...service, quantity, extra };
    setCart(prev => [...prev, item]);
    setOrderModalOpen(false);
  }

  function removeFromCart(idx) {
    setCart(prev => prev.filter((_, i) => i !== idx));
  }

  function subtotal() {
    let s = 0;
    for (const it of cart) {
      s += (currency === "USD" ? it.baseUSD : it.baseINR) * it.quantity;
    }
    return currency === "USD" ? s.toFixed(2) : s.toFixed(0);
  }

  function placeOrder() {
    // Placeholder: This should call your backend to create an order and redirect to payment.
    // Important backend requirements: user authentication, KYC for sellers/buyers, fraud checks,
    // delivery webhook from workers, dispute mechanism.
    alert("Order placed (demo). Implement backend API to store order and call payment gateway.");
    setCart([]);
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <header className="max-w-5xl mx-auto flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">MicroWorks — small tasks, real results</h1>
          <p className="text-sm text-gray-600">Professional micro-services: content creation, data entry, research, design and more. Honest pricing.</p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={country}
            onChange={e => setCountry(e.target.value)}
            className="border rounded px-2 py-1"
          >
            <option>India</option>
            <option>United States</option>
            <option>United Kingdom</option>
            <option>Other</option>
          </select>
          <select value={currency} onChange={e => setCurrency(e.target.value)} className="border rounded px-2 py-1">
            <option value="INR">INR (₹)</option>
            <option value="USD">USD ($)</option>
          </select>
          <button className="bg-blue-600 text-white px-3 py-1 rounded" onClick={() => setTosOpen(true)}>TOS & Safety</button>
        </div>
      </header>

      <main className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6">
        <section className="md:col-span-2">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {SERVICES.map(s => (
              <article key={s.id} className="bg-white p-4 rounded shadow-sm">
                <h3 className="font-semibold">{s.title}</h3>
                <p className="text-sm text-gray-600 mt-1">{s.unitLabel}</p>
                <div className="mt-3 flex items-center justify-between">
                  <div>
                    <div className="text-lg font-bold">{currency === "INR" ? `₹${s.baseINR}` : `$${s.baseUSD}`}</div>
                    <div className="text-xs text-gray-500">base price</div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="border px-3 py-1 rounded"
                      onClick={() => { setSelectedService(s); setOrderModalOpen(true); }}
                    >
                      Order
                    </button>
                    <button
                      className="px-3 py-1 rounded bg-gray-100"
                      onClick={() => alert('Quick view: ' + s.title)}
                    >
                      Quick view
                    </button>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </section>

        <aside className="bg-white p-4 rounded shadow-sm">
          <h4 className="font-semibold">Cart</h4>
          <div className="mt-3 space-y-2">
            {cart.length === 0 && <div className="text-sm text-gray-500">Cart is empty</div>}
            {cart.map((it, idx) => (
              <div key={idx} className="flex justify-between items-center">
                <div>
                  <div className="font-medium">{it.title}</div>
                  <div className="text-xs text-gray-500">{it.quantity} × {it.unitLabel}</div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{currency === 'INR' ? `₹${it.baseINR * it.quantity}` : `$${(it.baseUSD * it.quantity).toFixed(2)}`}</div>
                  <div className="flex gap-2 mt-1">
                    <button className="text-sm text-red-500" onClick={() => removeFromCart(idx)}>Remove</button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="border-t mt-4 pt-3">
            <div className="flex justify-between"><span>Subtotal</span><strong>{currency === 'INR' ? `₹${subtotal()}` : `$${subtotal()}`}</strong></div>
            <button disabled={cart.length===0} onClick={placeOrder} className={`mt-3 w-full py-2 rounded ${cart.length===0? 'bg-gray-300':'bg-green-600 text-white'}`}>
              {cart.length===0 ? 'Add items to order' : 'Proceed to payment (demo)'}
            </button>
          </div>

          <div className="mt-4 text-xs text-gray-500">
            Pricing guide: typical small tasks are offered at ₹5, ₹10 for micro quantities. For international clients choose USD pricing. Final prices may vary based on delivery time & verification.
          </div>
        </aside>
      </main>

      {/* Order modal */}
      {orderModalOpen && selectedService && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center p-4">
          <div className="bg-white rounded w-full max-w-md p-4">
            <h3 className="font-semibold">Order — {selectedService.title}</h3>
            <p className="text-sm text-gray-600">Unit: {selectedService.unitLabel}</p>
            <OrderForm
              service={selectedService}
              currency={currency}
              onCancel={() => setOrderModalOpen(false)}
              onAdd={(q, extra) => addToCart(selectedService, q, extra)}
            />
          </div>
        </div>
      )}

      {/* TOS modal */}
      {tosOpen && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center p-4">
          <div className="bg-white rounded w-full max-w-2xl p-6">
            <h3 className="text-lg font-bold">Terms of Service & Safety</h3>
            <p className="mt-3 text-sm text-gray-700">Important: By using MicroWorks you agree that all orders will follow platform rules and local laws. You will not request fraudulent or abusive actions (fake accounts created by automation, harassment, hate speech, doxxing, or anything illegal). Sellers must provide honest delivery reports. Buyers and sellers must complete KYC if requested. Use this platform responsibly.</p>
            <div className="mt-4 text-right"><button className="px-3 py-1 bg-blue-600 text-white rounded" onClick={() => setTosOpen(false)}>Close</button></div>
          </div>
        </div>
      )}

      <footer className="max-w-5xl mx-auto mt-8 text-center text-sm text-gray-500">MicroWorks prototype — replace demo flows with a secure backend and payment gateway integrations. Built for idea validation.</footer>
    </div>
  );
}

function OrderForm({ service, currency, onCancel, onAdd }) {
  const [quantity, setQuantity] = useState(1);
  const [details, setDetails] = useState("");

  function unitLabel() { return service.unitLabel; }

  return (
    <div className="mt-3">
      <label className="block text-sm">Quantity ({unitLabel()})</label>
      <input type="number" min={1} value={quantity} onChange={e=>setQuantity(Number(e.target.value))} className="w-full border rounded px-2 py-1 mt-1" />

      <label className="block text-sm mt-3">Target (username / page / video link)</label>
      <input value={details} onChange={e=>setDetails(e.target.value)} className="w-full border rounded px-2 py-1 mt-1" placeholder="e.g. @username or https://..." />

      <div className="mt-3 flex items-center justify-between">
        <div>
          <div className="text-sm text-gray-500">Estimated price</div>
          <div className="font-semibold">{currency === 'INR' ? `₹${service.baseINR * quantity}` : `$${(service.baseUSD * quantity).toFixed(2)}`}</div>
        </div>
        <div className="flex gap-2">
          <button className="px-3 py-1 rounded border" onClick={onCancel}>Cancel</button>
          <button className="px-3 py-1 rounded bg-indigo-600 text-white" onClick={() => onAdd(quantity, { details })}>Add to cart</button>
        </div>
      </div>

      <div className="mt-3 text-xs text-gray-500">Note: Actual delivery time may vary. Orders flagged as violating TOS will be canceled and refunded.</div>
    </div>
  );
}
