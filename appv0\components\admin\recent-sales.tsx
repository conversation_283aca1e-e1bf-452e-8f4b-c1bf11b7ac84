"use client";

import dynamic from "next/dynamic";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";

const RecentSalesContent = dynamic(() => import("./recent-sales-content"), {
  ssr: false,
  loading: () => (
    <div className="space-y-8">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="ml-4 space-y-1">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-4 w-[160px]" />
          </div>
        </div>
      ))}
    </div>
  ),
});

export function RecentSales() {
  return (
    <Suspense
      fallback={
        <div className="space-y-8">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="ml-4 space-y-1">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[160px]" />
              </div>
            </div>
          ))}
        </div>
      }
    >
      <RecentSalesContent />
    </Suspense>
  );
}
