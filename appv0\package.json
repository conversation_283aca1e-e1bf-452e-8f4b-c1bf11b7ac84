{"name": "islamic-art-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@types/bcryptjs": "^2.4.6", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^5.10.0", "lucide-react": "^0.487.0", "next": "^15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "recharts": "^2.15.2", "sonner": "^1.7.4", "tailwind-merge": "^3.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^20.17.30", "@types/react": "^18.3.20", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prisma": "^5.10.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}