<?php
include 'includes/db.php';
include 'includes/header.php';

$sql = "SELECT products.*, categories.name AS category_name FROM products 
        JOIN categories ON products.category_id = categories.id";
$result = $conn->query($sql);
?>

<h2>Manage Products</h2>
<a href="add_product.php">Add New Product</a>
<table border="1">
    <tr>
        <th>ID</th>
        <th>Name</th>
        <th>Category</th>
        <th>Price</th>
        <th>Stock</th>
        <th>Image</th>
        <th>Actions</th>
    </tr>
    <?php while ($row = $result->fetch_assoc()): ?>
    <tr>
        <td><?= $row['id'] ?></td>
        <td><?= $row['name'] ?></td>
        <td><?= $row['category_name'] ?></td>
        <td>₹<?= $row['price'] ?></td>
        <td><?= $row['stock'] ?></td>
        <td><img src="../assets/uploads/<?= $row['image'] ?>" width="50"></td>
        <td>
            <a href="edit_product.php?id=<?= $row['id'] ?>">Edit</a> |
            <a href="delete_product.php?id=<?= $row['id'] ?>" onclick="return confirm('Are you sure?')">Delete</a>
        </td>
    </tr>
    <?php endwhile; ?>
</table>

<?php include 'includes/footer.php'; ?>
