import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";

export default function AboutPage() {
  return (
    <div className="container py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8">About Artivist Creations</h1>

        <div className="relative h-[400px] w-full mb-12 rounded-lg overflow-hidden">
          <Image
            src="/placeholder.svg?height=400&width=800"
            alt="Artivist Creations Studio"
            fill
            className="object-cover"
          />
        </div>

        <div className="prose prose-lg max-w-none">
          <p className="text-lg mb-6">
            Artivist Creations is a premier destination for Islamic wall art and
            home decor, dedicated to bringing the beauty of Islamic art into
            modern living spaces. Founded in 2018, we combine traditional
            Islamic artistic elements with contemporary design to create pieces
            that resonate with both spiritual and aesthetic sensibilities.
          </p>

          <h2 className="text-2xl font-bold mt-12 mb-6">Our Mission</h2>
          <p className="mb-6">
            Our mission is to make Islamic art accessible and relevant to modern
            homes while maintaining the integrity and beauty of traditional
            Islamic artistic expressions. We believe that art has the power to
            transform spaces and uplift spirits, and we strive to create pieces
            that do both.
          </p>

          <h2 className="text-2xl font-bold mt-12 mb-6">Our Craftsmanship</h2>
          <p className="mb-6">
            Each piece in our collection is carefully crafted using premium
            materials including acrylic, metal, PVC, and wood. We work with
            skilled artisans who bring years of experience and passion to every
            creation. Our attention to detail and commitment to quality ensures
            that each piece is not just a decoration, but a work of art that
            tells a story.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-4">Our Values</h3>
                <ul className="space-y-2">
                  <li>• Authenticity in Islamic artistic expression</li>
                  <li>• Quality craftsmanship and materials</li>
                  <li>• Innovation in design and technique</li>
                  <li>• Customer satisfaction and service</li>
                  <li>• Sustainable and ethical practices</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-4">Our Promise</h3>
                <ul className="space-y-2">
                  <li>• Handcrafted with care and attention</li>
                  <li>• Premium materials and finishes</li>
                  <li>• Unique and meaningful designs</li>
                  <li>• Excellent customer service</li>
                  <li>• Worldwide shipping</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <h2 className="text-2xl font-bold mt-12 mb-6">Join Our Journey</h2>
          <p className="mb-6">
            We invite you to explore our collections and find pieces that speak
            to your heart and enhance your space. Whether you're looking for a
            statement piece for your living room or a meaningful gift for a
            loved one, we're here to help you find the perfect piece.
          </p>
        </div>
      </div>
    </div>
  );
}
