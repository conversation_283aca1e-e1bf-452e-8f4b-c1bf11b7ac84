<?php
require 'config.php';
$input=json_decode(file_get_contents('php://input'),true);
if(!$input||empty($input['name'])){echo json_encode(['success'=>false,'msg'=>'Missing name']);exit;}
$amount=CONTRIB_AMOUNT_PAISA;
$orderData=['amount'=>$amount,'currency'=>'INR','receipt'=>'rcpt_'.time(),'payment_capture'=>1];
$ch=curl_init('https://api.razorpay.com/v1/orders');
curl_setopt($ch,CURLOPT_USERPWD,RAZORPAY_KEY_ID.':'.RAZORPAY_KEY_SECRET);
curl_setopt($ch,CURLOPT_POSTFIELDS,json_encode($orderData));
curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
curl_setopt($ch,CURLOPT_HTTPHEADER,['Content-Type: application/json']);
$result=curl_exec($ch);
$http_status=curl_getinfo($ch,CURLINFO_HTTP_CODE);
curl_close($ch);
if($http_status!=200&&$http_status!=201){echo json_encode(['success'=>false]);exit;}
$order=json_decode($result,true);
echo json_encode(['success'=>true,'order'=>['id'=>$order['id'],'amount'=>$order['amount']]]);
?>