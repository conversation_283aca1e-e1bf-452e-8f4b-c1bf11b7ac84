<?php
require 'config.php';
$input=json_decode(file_get_contents('php://input'),true);
if(!$input){echo json_encode(['success'=>false]);exit;}
$name=$input['name']??'';$email=$input['email']??'';$city=$input['city']??'';
$razorpay_order_id=$input['razorpay_order_id']??'';$razorpay_payment_id=$input['razorpay_payment_id']??'';$razorpay_signature=$input['razorpay_signature']??'';
$payload=$razorpay_order_id.'|'.$razorpay_payment_id;
$expected_sig=hash_hmac('sha256',$payload,RAZORPAY_KEY_SECRET);
if(!hash_equals($expected_sig,$razorpay_signature)){echo json_encode(['success'=>false,'msg'=>'Signature verification failed']);exit;}
$pdo=getPDO();
$stmt=$pdo->prepare("INSERT INTO donors (name,email,city,amount,razorpay_order_id,razorpay_payment_id) VALUES (?,?,?,?,?,?)");
$stmt->execute([$name,$email,$city,300,$razorpay_order_id,$razorpay_payment_id]);
echo json_encode(['success'=>true]);
?>