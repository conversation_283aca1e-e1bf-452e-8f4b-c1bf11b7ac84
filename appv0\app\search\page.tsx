"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, ShoppingCart } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useCart } from "../context/CartContext";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  featuredProducts,
  latestProducts,
  newArrivals,
  bestSellers,
  homeDecor,
  wallArt,
  islamicArt,
} from "../data/products";

interface Product {
  id: string;
  name: string;
  material: string;
  price: number;
  image: string;
  isNew?: boolean;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const query = searchParams.get("q");
  const { user, toggleFavorite } = useAuth();
  const { items, addToCart } = useCart();
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (query) {
      // In a real application, you would make an API call here
      // For now, we'll simulate a search with the sample data
      const allProducts = [
        ...featuredProducts,
        ...latestProducts,
        ...newArrivals,
        ...bestSellers,
        ...homeDecor,
        ...wallArt,
        ...islamicArt,
      ];

      const results = allProducts.filter((product) =>
        product.name.toLowerCase().includes(query.toLowerCase())
      );

      setSearchResults(results);
      setLoading(false);
    }
  }, [query]);

  const handleFavorite = async (productId: string) => {
    if (!user) {
      toast.error("Please sign in to add favorites");
      router.push("/signin");
      return;
    }

    try {
      await toggleFavorite(productId);
      toast.success(
        user.favorites.includes(productId)
          ? "Removed from favorites"
          : "Added to favorites"
      );
    } catch (error) {
      toast.error("Failed to update favorites");
    }
  };

  const handleAddToCart = (product: Product) => {
    addToCart(product);
    toast.success("Added to cart");
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-8">Search Results for "{query}"</h1>
      {searchResults.length === 0 ? (
        <div className="text-center text-gray-500">
          No products found matching your search.
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {searchResults.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square">
                <img
                  src={product.image}
                  alt={product.name}
                  className="object-cover w-full h-full"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFavorite(product.id);
                    }}
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        user?.favorites.includes(product.id)
                          ? "fill-red-500 text-red-500"
                          : ""
                      }`}
                    />
                  </Button>
                  <Button
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                </div>
                {product.isNew && (
                  <Badge className="absolute top-2 left-2 bg-emerald-600">
                    New
                  </Badge>
                )}
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
