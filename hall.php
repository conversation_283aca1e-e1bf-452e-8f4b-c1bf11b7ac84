<?php
require 'config.php';
$pdo=getPDO();
$page=max(1,(int)($_GET['page']??1));
$perPage=50;$offset=($page-1)*$perPage;
$total=$pdo->query("SELECT COUNT(*) as cnt FROM donors")->fetch()['cnt'];
$stmt=$pdo->prepare("SELECT name,city,created_at FROM donors ORDER BY id DESC LIMIT ? OFFSET ?");
$stmt->bindValue(1,$perPage,PDO::PARAM_INT);
$stmt->bindValue(2,$offset,PDO::PARAM_INT);
$stmt->execute();
$donors=$stmt->fetchAll();
$last_page=ceil($total/$perPage);
?>
<!doctype html><html><head><meta charset="utf-8"><title>Hall of Helpers</title></head><body>
<h2>Hall of Helpers (Total <?= $total ?>)</h2>
<ul>
<?php foreach($donors as $d): ?>
<li><?= htmlspecialchars($d['name']) ?> <?= $d['city']?'- '.htmlspecialchars($d['city']):'' ?> (<?= $d['created_at'] ?>)</li>
<?php endforeach; ?>
</ul>
<div>
<?php if($page>1): ?><a href="?page=<?= $page-1 ?>">Prev</a><?php endif; ?>
Page <?= $page ?> of <?= $last_page ?>
<?php if($page<$last_page): ?><a href="?page=<?= $page+1 ?>">Next</a><?php endif; ?>
</div>
<a href="index.php">Back to Home</a>
</body></html>