import { NextResponse } from "next/server";
import { prisma } from "@/app/lib/db/schema";
import { hashPassword, createToken } from "@/app/lib/auth";

export async function POST(request: Request) {
  try {
    const { name, email, password } = await request.json();

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        favorites: "[]", // Store as JSON string
      },
      select: {
        id: true,
        name: true,
        email: true,
        favorites: true,
      },
    });

    // Create token
    const token = await createToken(user.id);

    // Set cookie
    const response = NextResponse.json({
      ...user,
      favorites: JSON.parse(user.favorites), // Parse back to array for response
    });
    response.cookies.set("token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 60 * 60 * 24, // 24 hours
      path: "/",
    });

    return response;
  } catch (error) {
    console.error("Signup error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
