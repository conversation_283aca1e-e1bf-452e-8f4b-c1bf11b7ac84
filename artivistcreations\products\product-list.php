<?php include('../includes/header.php'); ?>
<main>
    <section class="product-list">
        <h2>Our Products</h2>
        <div class="filter">
            <!-- Filters for category, price range, etc. -->
            <form method="GET" action="">
                <select name="category">
                    <option value="">Select Category</option>
                    <option value="wall-art">Wall Art</option>
                    <option value="home-accents">Home Accents</option>
                    <option value="custom">Custom Designs</option>
                </select>
                <button type="submit">Apply</button>
            </form>
        </div>
        <div class="product-grid">
            <!-- Display products dynamically -->
            <?php
                // Example array of products, this would normally come from a database
                $products = [
                    ['id' => 1, 'name' => 'Acrylic Wall Art', 'price' => 199, 'image' => 'product1.jpg'],
                    ['id' => 2, 'name' => 'Metal Sculpture', 'price' => 299, 'image' => 'product2.jpg'],
                    // Add more products here
                ];
                
                foreach ($products as $product) {
                    echo '<div class="product-item">';
                    echo '<img src="../assets/images/' . $product['image'] . '" alt="' . $product['name'] . '">';
                    echo '<p>' . $product['name'] . '</p>';
                    echo '<p>$' . $product['price'] . '</p>';
                    echo '<a href="product.php?id=' . $product['id'] . '" class="cta-button">View Details</a>';
                    echo '</div>';
                }
            ?>
        </div>
    </section>
</main>
<?php include('../includes/footer.php'); ?>
