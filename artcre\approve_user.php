<?php
// approve_user.php
require_once 'db.php';

$data = json_decode(file_get_contents("php://input"), true);
$userId = $data['userId'] ?? null;

if ($userId) {
    // Update query to set user status to 'approved'
    $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ?");
    if ($stmt->execute([$userId])) {
        echo json_encode(['success' => true, 'message' => 'User approved successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to approve user']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
}
?>
