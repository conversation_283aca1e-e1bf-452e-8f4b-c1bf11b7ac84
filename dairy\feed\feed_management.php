<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000" data-border-radius="small"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
                ><a href="../index.php">DairyPro</a></span>
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="../cattle/cattle_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Cattle</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Feed</a
              ><a
                href="../labour/labour_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Labour</a
              ><a
                href="../production/production_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Production</a
              ><a
                href="../sales/sales_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Sales</a
              ><a
                href="../reports.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Reporting</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
          </div>
        </div>
      </div>
    </nav>

<main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-4">
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-custom bg-opacity-10">
          <i class="fas fa-warehouse text-custom"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Feed Stock</p>
          <h3 class="text-xl font-semibold text-gray-900">2,450 kg</h3>
        </div>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100">
          <i class="fas fa-dollar-sign text-green-600"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Monthly Feed Cost</p>
          <h3 class="text-xl font-semibold text-gray-900">$5,240</h3>
        </div>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-yellow-100">
          <i class="fas fa-clock text-yellow-600"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Next Delivery Due</p>
          <h3 class="text-xl font-semibold text-gray-900">2 days</h3>
        </div>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-red-100">
          <i class="fas fa-exclamation-triangle text-red-600"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Low Stock Items</p>
          <h3 class="text-xl font-semibold text-gray-900">3 items</h3>
        </div>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mt-6">
    <div class="bg-white p-6 rounded-lg shadow lg:col-span-2">
      <div class="flex items-center mb-6 gap-4">
        <h2 class="text-lg font-semibold text-gray-900">Feed Inventory</h2>
        <button
          onClick="location.href='./add_feed.php'"
          class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
        >
          Add Feed Stock
        </button>
        <button
          onClick="location.href='./feed_consumption.php'"
          class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
        >
          Feed Consumption
        </button>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Feed Type
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Current Stock
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Reorder Level
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Status
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Last Updated
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Hay
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                850 kg
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                500 kg
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                  >Sufficient</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                2024-02-20
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Grain Mix
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                200 kg
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                300 kg
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800"
                  >Low Stock</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                2024-02-19
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Silage
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                1200 kg
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                1000 kg
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                  >Sufficient</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                2024-02-18
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">
        Feed Consumption Trends
      </h2>
      <div id="feed-chart" class="h-64"></div>
    </div>
  </div>
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 mt-6">
    <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-900">Feed Orders</h2>
        <button
          onClick="location.href='./purchase_fee.php'"
          class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
        >
          Place Order
        </button>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Order ID
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Supplier
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Items
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Amount
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Status
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                #F2401
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                FeedCo Ltd
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Grain Mix, Hay
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                $3,450
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                  >Delivered</span
                >
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                #F2402
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                AgriFeeds
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Silage
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                $2,800
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800"
                  >In Transit</span
                >
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Alerts</h2>
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <span
              class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-red-100"
              ><i class="fas fa-exclamation-triangle text-red-600"></i
            ></span>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Low Grain Mix Stock</p>
            <p class="text-sm text-gray-500">
              Current stock below reorder level
            </p>
          </div>
        </div>
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <span
              class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-yellow-100"
              ><i class="fas fa-truck text-yellow-600"></i
            ></span>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Delivery Expected</p>
            <p class="text-sm text-gray-500">Order #F2402 arriving tomorrow</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
<footer class="bg-white border-t border-gray-200 mt-8">
  <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-500">
        Active Feed Orders: 2 | Pending Deliveries: 1
      </div>
      <div class="flex space-x-4">
        <button
          class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
        >
          Reports
        </button>
        <button
          class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
        >
          Settings
        </button>
        <button
          class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
        >
          Support
        </button>
      </div>
    </div>
  </div>
</footer>
<script>
  function updateTime() {
    const now = new Date();
    document.getElementById("current-time").textContent = now.toLocaleString();
  }
  setInterval(updateTime, 1000);
  updateTime();
  const salesChart = echarts.init(document.getElementById("sales-chart"));
  const option = {
    animation: false,
    tooltip: {
      trigger: "axis",
    },
    xAxis: {
      type: "category",
      data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        type: "line",
        smooth: true,
        lineStyle: {
          color: "#4F46E5",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(79, 70, 229, 0.3)",
            },
            {
              offset: 1,
              color: "rgba(79, 70, 229, 0.1)",
            },
          ]),
        },
      },
    ],
  };
  salesChart.setOption(option);
  window.addEventListener("resize", function () {
    salesChart.resize();
  });
</script>

</body>
</html>
