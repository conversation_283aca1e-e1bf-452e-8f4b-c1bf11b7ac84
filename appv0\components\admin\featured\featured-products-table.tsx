"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Star } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"

// Sample featured products data
const initialFeaturedProducts = [
  {
    id: "1",
    name: "Allah Calligraphy",
    material: "Acrylic",
    price: 129.99,
    image: "/placeholder.svg?height=400&width=400",
    position: 1,
  },
  {
    id: "2",
    name: "Ayatul Kursi Frame",
    material: "Wood",
    price: 149.99,
    image: "/placeholder.svg?height=400&width=400",
    position: 2,
  },
  {
    id: "5",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    material: "Metal",
    price: 159.99,
    image: "/placeholder.svg?height=400&width=400",
    position: 3,
  },
  {
    id: "7",
    name: "99 Names of Allah",
    material: "Acrylic",
    price: 249.99,
    image: "/placeholder.svg?height=400&width=400",
    position: 4,
  },
]

export function FeaturedProductsTable() {
  const [featuredProducts, setFeaturedProducts] = useState(initialFeaturedProducts)
  const { toast } = useToast()

  const moveProduct = (id: string, direction: "up" | "down") => {
    const currentIndex = featuredProducts.findIndex((p) => p.id === id)
    if (
      (direction === "up" && currentIndex === 0) ||
      (direction === "down" && currentIndex === featuredProducts.length - 1)
    ) {
      return
    }

    const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1
    const newProducts = [...featuredProducts]

    // Swap positions
    const temp = newProducts[currentIndex]
    newProducts[currentIndex] = newProducts[newIndex]
    newProducts[newIndex] = temp

    // Update position numbers
    newProducts.forEach((product, index) => {
      product.position = index + 1
    })

    setFeaturedProducts(newProducts)

    toast({
      title: "Order updated",
      description: "The featured products order has been updated.",
    })
  }

  const removeFromFeatured = (id: string) => {
    const newProducts = featuredProducts.filter((p) => p.id !== id)

    // Update position numbers
    newProducts.forEach((product, index) => {
      product.position = index + 1
    })

    setFeaturedProducts(newProducts)

    toast({
      title: "Product removed",
      description: "The product has been removed from featured products.",
    })
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[80px]">Position</TableHead>
            <TableHead className="w-[80px]">Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Material</TableHead>
            <TableHead>Price</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {featuredProducts.map((product) => (
            <TableRow key={product.id}>
              <TableCell className="font-medium">{product.position}</TableCell>
              <TableCell>
                <div className="w-[50px] h-[50px] relative">
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    fill
                    className="object-cover rounded-md"
                  />
                </div>
              </TableCell>
              <TableCell>{product.name}</TableCell>
              <TableCell>{product.material}</TableCell>
              <TableCell>${product.price.toFixed(2)}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => moveProduct(product.id, "up")}
                    disabled={product.position === 1}
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => moveProduct(product.id, "down")}
                    disabled={product.position === featuredProducts.length}
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className="text-destructive hover:text-destructive"
                    onClick={() => removeFromFeatured(product.id)}
                  >
                    <Star className="h-4 w-4 fill-current" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

