"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Heart, ShoppingCart, User, Search, X } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useCart } from "../context/CartContext";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useState } from "react";

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const { items } = useCart();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const isActive = (path: string) => pathname === path;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setIsSearchOpen(false);
      setSearchQuery("");
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-xl font-bold text-emerald-600">
              Artivist Creations
            </Link>
            <nav className="hidden md:flex items-center space-x-8">
              <Link
                href="/"
                className={`text-sm font-medium ${
                  isActive("/")
                    ? "text-emerald-600"
                    : "text-gray-500 hover:text-gray-900"
                }`}
              >
                Home
              </Link>
              <Link
                href="/shop"
                className={`text-sm font-medium ${
                  isActive("/shop")
                    ? "text-emerald-600"
                    : "text-gray-500 hover:text-gray-900"
                }`}
              >
                Shop
              </Link>
              <Link
                href="/about"
                className={`text-sm font-medium ${
                  isActive("/about")
                    ? "text-emerald-600"
                    : "text-gray-500 hover:text-gray-900"
                }`}
              >
                About
              </Link>
              <Link
                href="/contact"
                className={`text-sm font-medium ${
                  isActive("/contact")
                    ? "text-emerald-600"
                    : "text-gray-500 hover:text-gray-900"
                }`}
              >
                Contact
              </Link>
            </nav>
            <div className="flex items-center space-x-6">
              {isSearchOpen ? (
                <form
                  onSubmit={handleSearch}
                  className="flex items-center space-x-2"
                >
                  <Input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-48"
                    autoFocus
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setIsSearchOpen(false);
                      setSearchQuery("");
                    }}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </form>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-500 hover:text-emerald-600"
                  onClick={() => setIsSearchOpen(true)}
                >
                  <Search className="h-5 w-5" />
                </Button>
              )}
              <Link href="/cart" className="relative">
                <ShoppingCart className="h-5 w-5 text-gray-500 hover:text-emerald-600" />
                {items.length > 0 && (
                  <Badge className="absolute -top-1 -right-1 bg-emerald-600">
                    {items.length}
                  </Badge>
                )}
              </Link>
              {user ? (
                <>
                  <Link href="/favorites" className="relative">
                    <Heart className="h-5 w-5 text-gray-500 hover:text-emerald-600" />
                    {user.favorites.length > 0 && (
                      <Badge className="absolute -top-1 -right-1 bg-emerald-600">
                        {user.favorites.length}
                      </Badge>
                    )}
                  </Link>
                  <div className="relative group">
                    <Button
                      variant="ghost"
                      className="flex items-center space-x-2 text-gray-500 hover:text-gray-900"
                    >
                      <User className="h-5 w-5" />
                      <span className="text-sm font-medium">{user.name}</span>
                    </Button>
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                      <Link
                        href="/profile"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Profile
                      </Link>
                      <button
                        onClick={() => signOut()}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <Link href="/signin">
                    <Button variant="ghost" size="sm">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/signup">
                    <Button size="sm">Sign Up</Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
      <footer className="bg-gray-50 border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">Artivist Creations</h3>
              <p className="text-gray-600">
                Handcrafted Islamic art and home decor pieces.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Shop</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/shop"
                    className="text-gray-600 hover:text-emerald-600"
                  >
                    All Products
                  </Link>
                </li>
                <li>
                  <Link
                    href="/shop?category=new"
                    className="text-gray-600 hover:text-emerald-600"
                  >
                    New Arrivals
                  </Link>
                </li>
                <li>
                  <Link
                    href="/shop?category=sale"
                    className="text-gray-600 hover:text-emerald-600"
                  >
                    Sale
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Customer Service</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/contact"
                    className="text-gray-600 hover:text-emerald-600"
                  >
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link
                    href="/shipping"
                    className="text-gray-600 hover:text-emerald-600"
                  >
                    Shipping & Returns
                  </Link>
                </li>
                <li>
                  <Link
                    href="/faq"
                    className="text-gray-600 hover:text-emerald-600"
                  >
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <ul className="space-y-2">
                <li className="text-gray-600">
                  Email: <EMAIL>
                </li>
                <li className="text-gray-600">Phone: +****************</li>
                <li className="text-gray-600">
                  Address: 123 Art Street, Creative City, CC 12345
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-200 text-center text-gray-600">
            <p>
              &copy; {new Date().getFullYear()} Artivist Creations. All rights
              reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
