<?php
// index.php

include('config.php');
session_start();

// Fetch featured products (limit to 5)
$featuredStmt = $pdo->prepare("SELECT * FROM products WHERE is_featured = 1 LIMIT 5");
$featuredStmt->execute();
$featuredProducts = $featuredStmt->fetchAll();

// Fetch categories that have at least one product
$categoriesStmt = $pdo->prepare("
    SELECT DISTINCT c.id, c.name 
    FROM categories c 
    JOIN products p ON c.id = p.category_id
");
$categoriesStmt->execute();
$categories = $categoriesStmt->fetchAll();

$categoryProducts = [];
foreach ($categories as $category) {
    $productsStmt = $pdo->prepare("SELECT * FROM products WHERE category_id = ?");
    $productsStmt->execute([$category['id']]);
    $categoryProducts[$category['id']] = $productsStmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Royal Arts</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.15.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500&display=swap');
    @import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css");
    </style>
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="images/logo.png" alt="Logo">
            <h1>Artivist Creations</h1>
        </div>
        <nav class="container add" id="container">
            <div class="navbar">
                <ul>
                    <li><a href="index.php"><i class="bi bi-house"></i><span> Home</span></a></li>
                    <?php
                        $whatsapp_number = "+918827634236";
                        $whatsapp_message = "Hello! I'm contacting you from your website.";
                        $whatsapp_url = "https://wa.me/" . preg_replace('/[^0-9]/', '', $whatsapp_number) . "?text=" . urlencode($whatsapp_message);
                    ?>
                    <li><a href="<?php echo $whatsapp_url; ?>" target="_blank"><i class="bi bi-whatsapp"></i><span>  Message Us</span></a></li>
                    <li><a href="cart.php"><i class="bi bi-cart"></i><span> Cart</span></a></li>                
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <li><a href="favourites.php"><i class="bi bi-heart"></i><span> Favourites</span></a></li>
                        <li><a href="profile.php"><i class="bi bi-person"></i><span> My Profile</span></a></li>  
                        <li><a href="logout.php"><i class="bi bi-box-arrow-in-right"></i><span> Log Out</span></a></li>                    
                    <?php else: ?>
                        <li><a href="login.php"><i class="bi bi-key"></i><span> Login</span></a></li>
                        <li><a href="register.php"><i class="bi bi-pen"></i><span> Register</span></a></li> 
                    <?php endif; ?>                               
                </ul>
            </div>
        </nav>
    </header>

    <!-- Banner Section -->
    <section class="banner">
        <div class="banner-content">
            <h2>Welcome to Our Store!</h2>
            <p>Discover amazing products and great deals.</p>
            <a href="products.php" class="cta-button">Shop</a>
        </div>
    </section>

    <section class="featured-products">
        <h2>Featured Products</h2>
        <div class="product-grid">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="product-item">
                    <img src="admin/uploads/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                    <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                    <p>Rs <?php echo number_format($product['price'], 2); ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <section class="categories-products">
        <?php foreach ($categories as $category): ?>
            <div class="category-section">
                <h2><?php echo htmlspecialchars($category['name']); ?></h2>
                <div class="product-grid">
                    <?php foreach ($categoryProducts[$category['id']] as $product): ?>
                        <div class="product-item">
                            <img src="admin/uploads/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                            <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                            <p>Rs <?php echo number_format($product['price'], 2); ?></p>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </section>

    <footer>
        <p>&copy; 2024 Royal Arts. All rights reserved.</p>
    </footer>
</body>
</html>
