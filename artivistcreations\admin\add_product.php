<?php
include 'includes/db.php';
include 'includes/header.php';

// Fetch categories for dropdown
$categories = $conn->query("SELECT * FROM categories");

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $category_id = $_POST['category_id'];
    $description = $_POST['description'];
    $price = $_POST['price'];
    $stock = $_POST['stock'];
    $status = $_POST['status'];

    // Handle Image Upload
    $image = $_FILES['image']['name'];
    $target = "../assets/uploads/" . basename($image);
    move_uploaded_file($_FILES['image']['tmp_name'], $target);

    $sql = "INSERT INTO products (name, category_id, description, price, stock, image, status) 
            VALUES ('$name', '$category_id', '$description', '$price', '$stock', '$image', '$status')";
    
    if ($conn->query($sql)) {
        echo "<p>Product added successfully!</p>";
    } else {
        echo "<p>Error: " . $conn->error . "</p>";
    }
}
?>

<h2>Add New Product</h2>
<form method="POST" enctype="multipart/form-data">
    Name: <input type="text" name="name" required><br>
    Category:
    <select name="category_id" required>
        <?php while ($cat = $categories->fetch_assoc()): ?>
            <option value="<?= $cat['id'] ?>"><?= $cat['name'] ?></option>
        <?php endwhile; ?>
    </select><br>
    Description: <textarea name="description"></textarea><br>
    Price: <input type="number" name="price" required><br>
    Stock: <input type="number" name="stock" required><br>
    Image: <input type="file" name="image" required><br>
    Status: 
    <select name="status">
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
    </select><br>
    <input type="submit" value="Add Product">
</form>

<?php include 'includes/footer.php'; ?>
