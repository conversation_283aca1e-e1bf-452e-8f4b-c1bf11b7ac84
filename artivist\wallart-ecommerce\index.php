
<?php
// Index.php - Main entry point for the application
// Handles routing and initialization

// Enable error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Load configuration
require_once 'config/config.php';

// Load core classes
require_once 'core/Database.php';
require_once 'core/Controller.php';
require_once 'core/Router.php';

// Initialize router
$router = new Router();

// Define routes
$router->addRoute('/', 'HomeController@index');
$router->addRoute('/login', 'AuthController@login');
$router->addRoute('/register', 'AuthController@register');
$router->addRoute('/dashboard', 'DashboardController@index');

// Process current request
$router->dispatch();