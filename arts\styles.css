/* General Styles */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

header {
  background-color: #1e81b0;
  color: white;
  padding: 10px 0;
  text-align: center;
}

header .logo h1 {
  margin: 0;
}

.logo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo img {
  width: 70px;
  height: 70px;
  margin-right: 10px;
}

nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  margin-top: 5px;
}

nav ul li {
  margin: 0 15px;
  margin-top: 5px;
}

nav ul li a {
  color: white;
  text-decoration: none;
}

.banner {
  background: url("images/ss.png") no-repeat center center;
  background-size: cover;
  color: white;
  text-align: center;
  padding: 50px 20px;
  margin-top: 10px;
}

.banner-content h2 {
  font-size: 2em;
  margin: 0;
}

.banner-content p {
  font-size: 1.2em;
}

.cta-button {
  background-color: #e74c3c;
  color: white;
  padding: 10px 20px;
  text-decoration: none;
  border-radius: 5px;
  font-size: 1em;
}

.cta-button:hover {
  background-color: #c0392b;
}

.featured-products {
  padding: 20px;
  text-align: center;
}

.featured-products h2 {
  margin-bottom: 20px;
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.product-item {
  flex: 1 1 calc(20% - 20px);
  box-sizing: border-box;
  border: 1px solid #ddd;
  padding: 10px;
  text-align: center;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
}

.product-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-item img {
  width: 100%;
  height: 200px; /* Set a fixed height */
  object-fit: cover; /* Maintain aspect ratio and cover the area */
}

.product-item h3 {
  margin: 10px 0 5px;
  font-size: 1.2em;
}

.product-item p {
  margin: 0;
  font-size: 1.1em;
  color: #e74c3c;
}

.view-details {
  display: inline-block;
  margin-top: 10px;
  padding: 5px 10px;
  color: #e74c3c;
  text-decoration: none;
  border: 1px solid #e74c3c;
  border-radius: 3px;
}

.view-details:hover {
  background-color: #e74c3c;
  color: white;
}

.pagination {
  text-align: center;
  margin: 20px 0;
}

.pagination a {
  margin: 0 5px;
  text-decoration: none;
  color: #333;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.pagination a:hover {
  background-color: #e74c3c;
  color: white;
}

footer {
  background-color: #1e81b0;
  color: white;
  text-align: center;
  padding: 10px 0;
  position: relative;
  width: 100%;
  bottom: 0;
}
/* styles.css */

/* Categories Section */
.categories {
  padding: 20px;
  background-color: #f9f9f9;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}

.category-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  width: calc(25% - 20px);
}

.category-item h3 {
  font-size: 1.2rem;
  margin: 10px 0;
}

.category-section h2 {
  font-size: 1.8rem;
  display: flex;
  justify-content: center;
}

.category-item p {
  margin: 5px 0;
  font-size: 1rem;
}

.category-item a {
  text-decoration: none;
  color: inherit;
}
