<?php
// category.php

include('config.php');

$categoryId = $_GET['id'];
$categoryStmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
$categoryStmt->execute([$categoryId]);
$category = $categoryStmt->fetch();

$productsStmt = $pdo->prepare("SELECT * FROM products WHERE category_id = ?");
$productsStmt->execute([$categoryId]);
$products = $productsStmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['name']); ?></title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <h1>Royal Arts</h1>
        </div>
        <nav>
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="products.php">Products</a></li>
                <li><a href="about_us.html">About Us</a></li>
                <li><a href="contact_us.html">Contact Us</a></li>
                <li><a href="login.php">Login</a></li>
            </ul>
        </nav>
    </header>

    <!-- Category Section -->
    <section class="category">
        <h2><?php echo htmlspecialchars($category['name']); ?></h2>
        <p><?php echo htmlspecialchars($category['description']); ?></p>

        <div class="product-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-item">
                    <img src="uploads/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                    <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                    <p>$<?php echo number_format($product['price'], 2); ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- Footer Section -->
    <footer>
        <p>&copy; 2024 My E-commerce Site. All rights reserved.</p>
    </footer>

    <script src="scripts.js"></script>
</body>
</html>
