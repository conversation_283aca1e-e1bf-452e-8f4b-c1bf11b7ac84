<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script
      src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js"
      data-color="#000000"
      data-border-radius="small"
    ></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
              ><a href="./labour_management.php">Worker Management</a></span
              >
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="./salaried_workers.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Salaried Workers</a
              ><a
                href="#"
                class="text-custom border-b-2 border-custom px-3 py-2 text-sm font-medium"
                >Daily Workers</a
              ><a
                href="./attendance_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Attendance</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
            
          </div>
        </div>
      </div>
    </nav>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid gap-6">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold">Daily Workers</h2>
            <div class="flex space-x-4">
              <button
                class="!rounded-button bg-white border border-gray-300 text-gray-600 px-4 py-2 text-sm font-medium"
              >
                <i class="fas fa-filter mr-2"></i>Filter</button
              ><button
                onclick="location.href='./add_labour.php'"
                class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
              >
                <i class="fas fa-plus mr-2"></i>Add Worker
              </button>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr class="bg-gray-50">
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Worker ID
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Name
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Skills
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Daily Rate
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    DW001
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    John Doe
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Masonry, Carpentry
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    $80/day
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                      >Active</span
                    >
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                  >
                    <button class="text-custom hover:text-custom-dark mr-3"
                      onclick="location.href='./edit_dialy_worker.php'">
                      <i class="fas fa-edit"></i></button
                    ><button class="text-red-600 hover:text-red-900">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    DW002
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Jane Smith
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Painting, Plumbing
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    $75/day
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                      >Active</span
                    >
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                  >
                    <button class="text-custom hover:text-custom-dark mr-3">
                      <i class="fas fa-edit"></i></button
                    ><button class="text-red-600 hover:text-red-900">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    DW003
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Robert Johnson
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    General Labor
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    $65/day
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800"
                      >On Leave</span
                    >
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                  >
                    <button class="text-custom hover:text-custom-dark mr-3"
                      onclick="location.href='./edit_dialy_worker.php'">
                      <i class="fas fa-edit"></i></button
                    ><button class="text-red-600 hover:text-red-900">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="mt-4 flex items-center justify-between">
            <div class="text-sm text-gray-500">
              Showing 1 to 3 of 18 entries
            </div>
            <div class="flex space-x-2">
              <button class="px-3 py-1 border rounded text-sm">&lt;</button
              ><button
                class="px-3 py-1 border rounded text-sm bg-custom text-white"
              >
                1</button
              ><button class="px-3 py-1 border rounded text-sm">2</button
              ><button class="px-3 py-1 border rounded text-sm">3</button
              ><button class="px-3 py-1 border rounded text-sm">&gt;</button>
            </div>
          </div>
        </div>
      </div>
    </main>
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Active Sessions: 45 | Last Backup: 2 hours ago
          </div>
          <div class="flex space-x-4">
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Reports
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Settings
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Support
            </button>
          </div>
        </div>
      </div>
    </footer>
    <script>
      function updateTime() {
        const now = new Date();
        document.getElementById("current-time").textContent =
          now.toLocaleString();
      }
      setInterval(updateTime, 1000);
      updateTime();
      const salesChart = echarts.init(document.getElementById("sales-chart"));
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#4F46E5",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(79, 70, 229, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(79, 70, 229, 0.1)",
                },
              ]),
            },
          },
        ],
      };
      salesChart.setOption(option);
      window.addEventListener("resize", function () {
        salesChart.resize();
      });
    </script>
  </body>
</html>
