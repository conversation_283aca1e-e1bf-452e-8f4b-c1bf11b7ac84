import { NextResponse } from "next/server";
import { prisma } from "@/app/lib/db/schema";
import { getCurrentUser } from "@/app/lib/auth";

// Get user's favorites
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    return NextResponse.json({ favorites: user.favorites });
  } catch (error) {
    console.error("Get favorites error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Add or remove favorite
export async function POST(request: Request) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const { productId } = await request.json();
    const favorites = user.favorites;
    const isFavorite = favorites.includes(productId);

    // Toggle favorite
    const updatedFavorites = isFavorite
      ? favorites.filter((id) => id !== productId)
      : [...favorites, productId];

    // Update user's favorites
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { favorites: JSON.stringify(updatedFavorites) },
      select: { favorites: true },
    });

    return NextResponse.json({
      favorites: JSON.parse(updatedUser.favorites) as string[],
    });
  } catch (error) {
    console.error("Update favorites error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
