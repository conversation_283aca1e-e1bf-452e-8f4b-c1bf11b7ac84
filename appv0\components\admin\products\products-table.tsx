"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>, MoreHorizontal, Star, Trash } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"

// Sample product data
const initialProducts = [
  {
    id: "1",
    name: "Allah Calligraphy",
    material: "Acrylic",
    price: 129.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 25,
    featured: true,
  },
  {
    id: "2",
    name: "Ayatu<PERSON> Kursi Frame",
    material: "Wood",
    price: 149.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 15,
    featured: true,
  },
  {
    id: "3",
    name: "Geometric Pattern",
    material: "Metal",
    price: 199.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 10,
    featured: false,
  },
  {
    id: "4",
    name: "Kaaba Silhouette",
    material: "PVC",
    price: 89.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 30,
    featured: false,
  },
  {
    id: "5",
    name: "Bismillah Script",
    material: "Metal",
    price: 159.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 20,
    featured: false,
  },
  {
    id: "6",
    name: "Mosque Silhouette",
    material: "Wood",
    price: 119.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 18,
    featured: false,
  },
  {
    id: "7",
    name: "99 Names of Allah",
    material: "Acrylic",
    price: 249.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 8,
    featured: false,
  },
  {
    id: "8",
    name: "Islamic Geometric Art",
    material: "PVC",
    price: 99.99,
    image: "/placeholder.svg?height=400&width=400",
    inventory: 22,
    featured: false,
  },
]

export function ProductsTable() {
  const [products, setProducts] = useState(initialProducts)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [productToDelete, setProductToDelete] = useState<string | null>(null)
  const { toast } = useToast()

  const handleDeleteProduct = async (id: string) => {
    try {
      // In a real app, this would call the server action
      // await deleteProduct(id)

      // For demo purposes, we'll just update the state
      setProducts(products.filter((product) => product.id !== id))

      toast({
        title: "Product deleted",
        description: "The product has been deleted successfully.",
      })

      setDeleteDialogOpen(false)
      setProductToDelete(null)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete product. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleToggleFeatured = async (id: string, featured: boolean) => {
    try {
      // In a real app, this would call the server action
      // await toggleFeaturedProduct(id, !featured)

      // For demo purposes, we'll just update the state
      setProducts(products.map((product) => (product.id === id ? { ...product, featured: !featured } : product)))

      toast({
        title: featured ? "Removed from featured" : "Added to featured",
        description: `The product has been ${featured ? "removed from" : "added to"} featured products.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update featured status. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Image</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Material</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Inventory</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map((product) => (
              <TableRow key={product.id}>
                <TableCell>
                  <div className="w-[50px] h-[50px] relative">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-cover rounded-md"
                    />
                  </div>
                </TableCell>
                <TableCell className="font-medium">{product.name}</TableCell>
                <TableCell>{product.material}</TableCell>
                <TableCell>${product.price.toFixed(2)}</TableCell>
                <TableCell>
                  <Badge variant={product.inventory < 10 ? "destructive" : "outline"}>
                    {product.inventory} in stock
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button
                    variant={product.featured ? "default" : "outline"}
                    size="icon"
                    onClick={() => handleToggleFeatured(product.id, product.featured)}
                  >
                    <Star className={`h-4 w-4 ${product.featured ? "fill-current" : ""}`} />
                  </Button>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/products/${product.id}`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => {
                          setProductToDelete(product.id)
                          setDeleteDialogOpen(true)
                        }}
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the product from the database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => productToDelete && handleDeleteProduct(productToDelete)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

