import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    // Get the start of the current year
    const startOfYear = new Date();
    startOfYear.setMonth(0, 1);
    startOfYear.setHours(0, 0, 0, 0);

    // Get all completed orders for this year
    const orders = await prisma.order.findMany({
      where: {
        status: "completed",
        createdAt: {
          gte: startOfYear,
        },
      },
      select: {
        total: true,
        createdAt: true,
      },
    });

    // Group orders by month and calculate total sales
    const monthlySales = Array.from({ length: 12 }, (_, i) => {
      const month = new Date().getMonth() >= i ? i : -1;
      if (month === -1) return { name: getMonthName(i), total: 0 };

      const monthOrders = orders.filter(
        (order) => order.createdAt.getMonth() === i
      );
      const total = monthOrders.reduce((acc, order) => acc + order.total, 0);

      return {
        name: getMonthName(i),
        total: Math.round(total * 100) / 100,
      };
    });

    return NextResponse.json(monthlySales);
  } catch (error) {
    console.error("Error fetching overview data:", error);
    return NextResponse.json(
      { error: "Failed to fetch overview data" },
      { status: 500 }
    );
  }
}

function getMonthName(month: number): string {
  return new Date(0, month).toLocaleString("default", { month: "short" });
}
