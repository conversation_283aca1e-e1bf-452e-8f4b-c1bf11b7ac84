<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script
      src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js"
      data-color="#000000"
      data-border-radius="small"
    ></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
                >DairyPro</span
              >
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Dashboard</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Feed</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Labour</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Production</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Sales</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Reporting</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
            <button
              class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
            >
              <i class="fas fa-plus mr-2"></i>Quick Add
            </button>
          </div>
        </div>
      </div>
    </nav>
    <div class="bg-gray-100 border-b border-gray-200" id="sub-menu-bar">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          class="flex items-center space-x-6 overflow-x-auto py-3 scrollbar-hide"
        >
          <a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Record by Cattle</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Milk</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Ghee</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Paneer</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Dahi</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Chhas</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Makhkhan</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Dung</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Urine</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Bio Fertilizer</a
          ><a
            href="#"
            class="text-custom hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Bio CNG</a
          >
        </div>
      </div>
    </div>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div id="add-entry-form" class="bg-white p-6 rounded-lg shadow mb-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-lg font-semibold text-gray-900">
            Add Bio CNG Production Entry
          </h2>
          <button class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Date</label
              ><input
                type="date"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Input Amount (kg)</label
              ><input
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                placeholder="Enter amount in kg"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Output Amount (m³)</label
              ><input
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                placeholder="Enter amount in m³"
              />
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Quality Level</label
              ><select
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
              >
                <option>High</option>
                <option>Medium</option>
                <option>Low</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Notes</label
              ><textarea
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                rows="4"
                placeholder="Add any additional notes"
              ></textarea>
            </div>
          </div>
        </div>
        <div class="mt-6 flex justify-end space-x-4">
          <button
            class="!rounded-button px-4 py-2 text-sm font-medium border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel</button
          ><button
            class="!rounded-button px-4 py-2 text-sm font-medium bg-custom text-white hover:bg-custom/90"
          >
            Save Entry
          </button>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-4">
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <i class="fas fa-gas-pump text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">
                Total Bio CNG Production
              </p>
              <h3 class="text-xl font-semibold text-gray-900">800 m³</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <i class="fas fa-chart-line text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Efficiency Rate</p>
              <h3 class="text-xl font-semibold text-gray-900">95.2%</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100">
              <i class="fas fa-dollar-sign text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Cost Savings</p>
              <h3 class="text-xl font-semibold text-gray-900">₹45,000</h3>
            </div>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100">
              <i class="fas fa-leaf text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Carbon Offset</p>
              <h3 class="text-xl font-semibold text-gray-900">2.5 Tons</h3>
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-6 mt-6">
        <div id="edit-storage-form" class="bg-white p-6 rounded-lg shadow mb-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-semibold text-gray-900">
              Edit Storage Status
            </h2>
            <button class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >Current Storage (m³)</label
                ><input
                  type="number"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                  value="2400"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >Total Capacity (m³)</label
                ><input
                  type="number"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                  value="3000"
                />
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >Last Updated Date</label
                ><input
                  type="datetime-local"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >Notes</label
                ><textarea
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                  rows="4"
                  placeholder="Add any additional notes"
                ></textarea>
              </div>
            </div>
          </div>
          <div class="mt-6 flex justify-end space-x-4">
            <button
              class="!rounded-button px-4 py-2 text-sm font-medium border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel</button
            ><button
              class="!rounded-button px-4 py-2 text-sm font-medium bg-custom text-white hover:bg-custom/90"
            >
              Update Storage
            </button>
          </div>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold text-gray-900">
                Bio CNG Production Log
              </h2>
              <button
                class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
              >
                Add Entry
              </button>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      Date
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      Input (kg)
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      Output (m³)
                    </th>
                    <th
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      Quality
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 text-sm text-gray-900">2024-01-20</td>
                    <td class="px-6 py-4 text-sm text-gray-900">1,200</td>
                    <td class="px-6 py-4 text-sm text-gray-900">800</td>
                    <td class="px-6 py-4">
                      <span
                        class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                        >High</span
                      >
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold text-gray-900">
                System Parameters
              </h2>
              <select class="border-gray-300 rounded-md text-sm">
                <option>Real-time</option>
                <option>Hourly</option>
                <option>Daily</option>
              </select>
            </div>
            <div id="biocng-parameters" class="grid grid-cols-2 gap-4">
              <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm font-medium text-gray-600">Temperature</p>
                <p class="text-xl font-semibold text-gray-900">37.5°C</p>
              </div>
              <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm font-medium text-gray-600">Pressure</p>
                <p class="text-xl font-semibold text-gray-900">5.2 bar</p>
              </div>
              <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm font-medium text-gray-600">pH Level</p>
                <p class="text-xl font-semibold text-gray-900">7.2</p>
              </div>
              <div class="p-4 bg-gray-50 rounded-lg">
                <p class="text-sm font-medium text-gray-600">Methane Content</p>
                <p class="text-xl font-semibold text-gray-900">92%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-6 mt-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Storage Status
            </h2>
            <div class="grid grid-cols-2 gap-4">
              <div
                class="bg-gray-50 p-4 rounded-lg cursor-pointer hover:bg-gray-100"
              >
                <p class="text-sm font-medium text-gray-600">Current Storage</p>
                <p class="text-xl font-semibold text-gray-900">2,400 m³</p>
                <p class="text-xs text-gray-500">80% of capacity</p>
              </div>
              <div
                class="bg-gray-50 p-4 rounded-lg cursor-pointer hover:bg-gray-100"
              >
                <p class="text-sm font-medium text-gray-600">
                  Available Capacity
                </p>
                <p class="text-xl font-semibold text-gray-900">600 m³</p>
                <p class="text-xs text-gray-500">20% remaining</p>
              </div>
            </div>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Distribution Network
            </h2>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-600"
                  >Vehicle Fuel Station</span
                ><span class="text-sm font-semibold text-gray-900"
                  >450 m³/day</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-600"
                  >Industrial Units</span
                ><span class="text-sm font-semibold text-gray-900"
                  >250 m³/day</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-600"
                  >Local Pipeline</span
                ><span class="text-sm font-semibold text-gray-900"
                  >100 m³/day</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Today&#39;s Transactions: 45 | Pending Orders: 12
          </div>
          <div class="flex space-x-4">
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Reports
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Settings
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Support
            </button>
          </div>
        </div>
      </div>
    </footer>
    <script>
      function updateTime() {
        const now = new Date();
        document.getElementById("current-time").textContent =
          now.toLocaleString();
      }
      setInterval(updateTime, 1000);
      updateTime();
      const salesChart = echarts.init(document.getElementById("sales-chart"));
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#4F46E5",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(79, 70, 229, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(79, 70, 229, 0.1)",
                },
              ]),
            },
          },
        ],
      };
      salesChart.setOption(option);
      window.addEventListener("resize", function () {
        salesChart.resize();
      });
    </script>
  </body>
</html>
