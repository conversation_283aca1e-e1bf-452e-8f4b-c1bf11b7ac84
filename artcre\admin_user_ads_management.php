<?php
session_start();
require_once 'db.php';

// Fetch all user-posted ads
$stmt = $pdo->query("
    SELECT ads.*, users.username, categories.name AS category_name 
    FROM ads 
    JOIN users ON ads.user_id = users.id 
    JOIN categories ON ads.category_id = categories.id
");


$userAds = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<link rel="stylesheet" href="ad_sec_manage.css">
<link rel="stylesheet" href="sidebar.css">
<?php include 'admin_sidebar.php'; ?>

<div class="content">
    <h3>User Ads Management</h3>
    

    <table>
        <tr>
            <th>Title</th>
            <th>Description</th>
            <th>Category</th>
            <th>Price</th>
            <th>Image</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
        <?php foreach ($userAds as $ad): ?>
            <tr>
                <td><?php echo htmlspecialchars($ad['title']); ?></td>
                <td><?php echo htmlspecialchars($ad['description']); ?></td>
                <td><?php echo htmlspecialchars($ad['category_name']); ?></td>
                <td>₹<?php echo htmlspecialchars($ad['price']); ?></td>
                <td><img src="assets/uploads/images/<?php echo htmlspecialchars($ad['image']); ?>" alt="<?php echo htmlspecialchars($ad['title']); ?>" width="50"></td>
                <td><?php echo ucfirst($ad['status']); ?></td>
                <td>
                    <!-- Approve button -->
                    <form action="update_ad_status.php" method="POST" style="display:inline;">
                        <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                        <input type="hidden" name="status" value="approved">
                        <button type="submit">Approve</button>
                    </form>
                    
                    <!-- Hold button -->
                    <form action="update_ad_status.php" method="POST" style="display:inline;">
                        <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                        <input type="hidden" name="status" value="on_hold">
                        <button type="submit">Hold</button>
                    </form>
                    
                    <!-- Edit button -->
                    <form action="edit_ad_by_admin.php" method="GET" style="display:inline;">
                        <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                        <button type="submit">Edit</button>
                    </form>


                    
                    <!-- Delete button -->
                    <form action="delete_user_ad.php" method="POST" style="display:inline;">
                        <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                        <button type="submit" onclick="return confirm('Are you sure you want to delete this ad?')">Delete</button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
    </table>
</div>
