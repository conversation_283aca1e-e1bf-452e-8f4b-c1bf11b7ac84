import { SignJWT, jwtVerify } from "jose";
import { cookies } from "next/headers";
import { prisma } from "./db/schema";
import bcrypt from "bcrypt";

const secret = new TextEncoder().encode(
  process.env.JWT_SECRET || "default-secret-key"
);

export interface User {
  id: string;
  name: string;
  email: string;
  favorites: string[];
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10);
}

export async function comparePasswords(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

export async function createToken(userId: string): Promise<string> {
  return new SignJWT({ userId })
    .setProtectedHeader({ alg: "HS256" })
    .setExpirationTime("24h")
    .sign(secret);
}

export async function verifyToken(token: string) {
  try {
    const verified = await jwtVerify(token, secret);
    return verified.payload as { userId: string };
  } catch (error) {
    return null;
  }
}

export async function getCurrentUser(): Promise<User | null> {
  try {
    const token = cookies().get("token")?.value;
    if (!token) return null;

    const verified = await verifyToken(token);
    if (!verified) return null;

    const user = await prisma.user.findUnique({
      where: { id: verified.userId },
      select: {
        id: true,
        name: true,
        email: true,
        favorites: true,
      },
    });

    if (!user) return null;

    return {
      ...user,
      favorites: JSON.parse(user.favorites) as string[],
    };
  } catch (error) {
    console.error("Get current user error:", error);
    return null;
  }
}
