import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { Heart } from "lucide-react";

export default function ShopPage() {
  return (
    <main className="container py-16">
      <h1 className="text-4xl font-bold mb-8">Shop</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <Card key={product.id} className="overflow-hidden group">
            <div className="relative aspect-square overflow-hidden">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
              <Button
                size="icon"
                variant="secondary"
                className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Heart className="h-4 w-4" />
              </Button>
            </div>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium">{product.name}</h3>
                  <p className="text-sm text-gray-500">{product.material}</p>
                </div>
                <div className="text-emerald-600 font-semibold">
                  ${product.price}
                </div>
              </div>
              <Button className="w-full mt-4 bg-emerald-600 hover:bg-emerald-700">
                Add to Cart
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </main>
  );
}

const products = [
  {
    id: "1",
    name: "Allah Calligraphy",
    material: "Acrylic",
    price: 129.99,
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    id: "2",
    name: "Ayatul Kursi Frame",
    material: "Wood",
    price: 149.99,
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    id: "3",
    name: "Geometric Pattern",
    material: "Metal",
    price: 199.99,
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    id: "4",
    name: "Kaaba Silhouette",
    material: "PVC",
    price: 89.99,
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    id: "5",
    name: "Bismillah Script",
    material: "Metal",
    price: 159.99,
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    id: "6",
    name: "Mosque Silhouette",
    material: "Wood",
    price: 119.99,
    image: "/placeholder.svg?height=400&width=400",
  },
];
