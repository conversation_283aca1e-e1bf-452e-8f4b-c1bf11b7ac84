/* styles.css */

/* General Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

/* Admin Container */
.admin-container {
    display: flex;
    height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: #2c3e50; /* Matching sidebar color */
    color: #ecf0f1; /* Text color */
    padding-top: 20px;
}

.sidebar h2 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5em;
    color: #ecf0f1;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar ul li {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #34495e;
}

.sidebar ul li a {
    color: #ecf0f1; /* Link color */
    text-decoration: none;
    font-size: 1.1em;
    display: block;
}

.sidebar ul li:hover {
    background-color: #34495e; /* Hover effect */
}

/* Main Content Styles */
.main-content {
    flex-grow: 1;
    padding: 20px;
    background-color: #ecf0f1; /* Match background color */
}

.main-content h1 {
    color: #2c3e50; /* Main content heading color */
    margin-bottom: 10px;
}

/* User Management Styles */
.container {
    max-width: 800px;
    margin: auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #007bff; /* Table header color */
    color: white;
}

tr:hover {
    background-color: #f1f1f1;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    background-color: #28a745; /* Button color */
    color: white;
    cursor: pointer;
}

button:hover {
    background-color: #218838; /* Button hover color */
}

.search-bar {
    margin-bottom: 20px;
}
