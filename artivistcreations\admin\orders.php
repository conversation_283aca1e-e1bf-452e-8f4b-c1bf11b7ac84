<?php
include "auth.php";
include "../config/config.php";

// Fetch orders
$result = $conn->query("SELECT * FROM orders ORDER BY created_at DESC");

// Handle order status update
if (isset($_POST["update"])) {
    $order_id = $_POST["order_id"];
    $status = $_POST["status"];
    $conn->query("UPDATE orders SET status = '$status' WHERE id = $order_id");
    echo "<script>alert('Order updated!'); window.location.href = 'orders.php';</script>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <title>Manage Orders</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <h2>Manage Orders</h2>
    <table border="1">
        <tr>
            <th>ID</th>
            <th>User ID</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Action</th>
        </tr>
        <?php while ($row = $result->fetch_assoc()) { ?>
            <tr>
                <td><?php echo $row["id"]; ?></td>
                <td><?php echo $row["user_id"]; ?></td>
                <td><?php echo $row["total_amount"]; ?></td>
                <td><?php echo $row["status"]; ?></td>
                <td>
                    <form method="POST">
                        <input type="hidden" name="order_id" value="<?php echo $row['id']; ?>">
                        <select name="status">
                            <option value="Pending" <?php if ($row["status"] == "Pending") echo "selected"; ?>>Pending</option>
                            <option value="Shipped" <?php if ($row["status"] == "Shipped") echo "selected"; ?>>Shipped</option>
                            <option value="Delivered" <?php if ($row["status"] == "Delivered") echo "selected"; ?>>Delivered</option>
                        </select>
                        <button type="submit" name="update">Update</button>
                    </form>
                </td>
            </tr>
        <?php } ?>
    </table>
</body>
</html>
