<?php
include 'db.php';
session_start();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$categoryId = $_GET['id'];

// Fetch category details
$categoryStmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
$categoryStmt->execute([$categoryId]);
$category = $categoryStmt->fetch();

if (!$category) {
    header("Location: index.php");
    exit();
}

// Fetch ads for the selected category
$adsStmt = $pdo->prepare("SELECT ads.*, users.username AS posted_by 
                           FROM ads 
                           LEFT JOIN users ON ads.user_id = users.id 
                           WHERE ads.category_id = ? 
                           ORDER BY ads.created_at DESC");
$adsStmt->execute([$categoryId]);
$ads = $adsStmt->fetchAll(PDO::FETCH_ASSOC);

// Debugging output to check if ads are fetched
if (empty($ads)) {
    echo "<p>No ads found in this category.</p>";
} else {
    echo "<p>Found " . count($ads) . " ads in this category.</p>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><?= htmlspecialchars($category['name']) ?> - OLX Clone</title>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="container">
        <h1>Ads in Category: <?= htmlspecialchars($category['name']) ?></h1>
        <div class="ads-grid">
            <?php foreach ($ads as $ad): ?>
                <div class="ad-item">
                    <?php
                    $imagePath = "assets/uploads/images/" . $ad['image'];
                    if (!empty($ad['image']) && file_exists($imagePath)) {
                        echo "<img src='$imagePath' alt='Ad Image'>";
                    } else {
                        echo "<img src='assets/uploads/images/default.jpg' alt='Default Image'>";
                    }
                    ?>
                    <h3><a href="ad_details.php?id=<?php echo $ad['id']; ?>"><?php echo htmlspecialchars($ad['title']); ?></a></h3>
                    <p>Price: ₹<?php echo htmlspecialchars($ad['price']); ?></p>
                    <p>Posted by: <?php echo htmlspecialchars($ad['posted_by']); ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <?php include 'footer.php'; ?>
</body>
</html>
