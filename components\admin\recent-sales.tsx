import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function RecentSales() {
  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder.svg" alt="Avatar" />
          <AvatarFallback>SA</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none"><PERSON></p>
          <p className="text-sm text-muted-foreground"><EMAIL></p>
        </div>
        <div className="ml-auto font-medium">+$249.99</div>
      </div>
      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder.svg" alt="Avatar" />
          <AvatarFallback>MA</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none"><PERSON></p>
          <p className="text-sm text-muted-foreground"><EMAIL></p>
        </div>
        <div className="ml-auto font-medium">+$149.99</div>
      </div>
      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder.svg" alt="Avatar" />
          <AvatarFallback>FK</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Fatima Khan</p>
          <p className="text-sm text-muted-foreground"><EMAIL></p>
        </div>
        <div className="ml-auto font-medium">+$299.99</div>
      </div>
      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder.svg" alt="Avatar" />
          <AvatarFallback>AB</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Ahmed Baig</p>
          <p className="text-sm text-muted-foreground"><EMAIL></p>
        </div>
        <div className="ml-auto font-medium">+$99.99</div>
      </div>
      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder.svg" alt="Avatar" />
          <AvatarFallback>ZM</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1">
          <p className="text-sm font-medium leading-none">Zainab Malik</p>
          <p className="text-sm text-muted-foreground"><EMAIL></p>
        </div>
        <div className="ml-auto font-medium">+$199.99</div>
      </div>
    </div>
  )
}

