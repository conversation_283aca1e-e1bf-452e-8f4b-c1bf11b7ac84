<?php
session_start();
require_once 'db.php'; // Ensure this includes your PDO connection

// Fetch active and queued advertisements
$stmt = $pdo->query("SELECT * FROM advertisements WHERE is_queued = 0 AND start_date <= NOW() AND end_date >= NOW()");
$activeAd = $stmt->fetch(PDO::FETCH_ASSOC);

// Fetch queued advertisements
$queuedStmt = $pdo->query("SELECT * FROM advertisements WHERE is_queued = 1");


// Fetch resting ads
$restingAdsStmt = $pdo->query("SELECT * FROM advertisements WHERE is_queued = 0");
$restingAds = $restingAdsStmt->fetchAll(PDO::FETCH_ASSOC);


// Fetch categories for the dropdown
$categoriesStmt = $pdo->query("SELECT id, name FROM categories");
$categories = $categoriesStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<link rel="stylesheet" href="ad_sec_manage.css">
<link rel="stylesheet" href="sidebar.css">
<?php include 'admin_sidebar.php'; ?>
<div class="content">
    <h2>Advertisement Section Management</h2>
    

    <!-- Form to Add Advertisement -->
    <form action="add_advertisement.php" method="POST" enctype="multipart/form-data">
        <h3>Create New Advertisement</h3>
        <label for="title">Title:</label>
        <input type="text" name="title" required>
        
        <!-- Category Dropdown -->
        <label for="category">Category:</label>
        <select name="category" required>
            <option value="">Select Category</option>
            <?php foreach ($categories as $category): ?>
                <option value="<?php echo htmlspecialchars($category['id']); ?>">
                    <?php echo htmlspecialchars($category['name']); ?>
                </option>
            <?php endforeach; ?>
        </select>
        
        <label for="description">Description:</label>
        <textarea name="description" required></textarea>
        <label for="price">Price:</label>
        <input type="number" name="price" required>
        <label for="image">Upload Image:</label>
        <input type="file" name="image" accept="image/*" required>
        <label for="days">Time Limit (Days):</label>
        <input type="number" name="days" required>
        <button type="submit">Add Advertisement</button>
    </form>

    
    <!-- Active Advertisement Display -->
<h3>Active Advertisement</h3>
<?php if ($activeAd): ?>
    <div class="active-advertisement">
        <h4><?php echo htmlspecialchars($activeAd['title']); ?></h4>
        <p><?php echo htmlspecialchars($activeAd['description']); ?></p>
        <img src="uploads/ads/<?php echo htmlspecialchars($activeAd['image']); ?>" alt="<?php echo htmlspecialchars($activeAd['title']); ?>">
        <p>Price: ₹<?php echo htmlspecialchars($activeAd['price']); ?></p>
        <p>Start Date: <?php echo htmlspecialchars($activeAd['start_date']); ?></p>
        <p>End Date: <?php echo htmlspecialchars($activeAd['end_date']); ?></p>
        
        <!-- Button to Remove Active Advertisement -->
        <form action="remove_active_ad.php" method="POST" style="display:inline;">
            <input type="hidden" name="ad_id" value="<?php echo $activeAd['id']; ?>">
            <button type="submit" onclick="return confirm('Are you sure you want to remove this active ad?');">Remove Active Advertisement</button>
        </form>
    </div>
<?php else: ?>
    <p>No active advertisements at this time.</p>
<?php endif; ?>


    <!-- Queued Advertisements Section -->
    <h3>Queued Advertisements</h3>
    <table>
        <tr>
            <th>Title</th>
            <th>Category</th>
            <th>Price</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Status</th>
            <th>Actions</th> <!-- Add Actions column -->
        </tr>
        <?php while ($ad = $queuedStmt->fetch(PDO::FETCH_ASSOC)): ?>
            <tr>
                <td><?php echo htmlspecialchars($ad['title']); ?></td>
                <td><?php echo htmlspecialchars($ad['category']); ?></td>
                <td>₹<?php echo htmlspecialchars($ad['price']); ?></td>
                <td><?php echo htmlspecialchars($ad['start_date']); ?></td>
                <td><?php echo htmlspecialchars($ad['end_date']); ?></td>
                <td><?php echo $ad['is_queued'] ? "Queued" : "Active"; ?></td>
                <td>
                    <?php if ($ad['is_queued']): ?>
                        <form action="change_ad_status.php" method="POST" style="display:inline;">
                            <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                            <button type="submit" name="action" value="activate">Make Active</button>
                        </form>
                    <?php else: ?>
                        <form action="change_ad_status.php" method="POST" style="display:inline;">
                            <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                            <button type="submit" name="action" value="queue">Make Queued</button>
                        </form>
                    <?php endif; ?>
                    
                    <!-- Remove Advertisement Button -->
                    <form action="update_queue_status.php" method="POST" style="display:inline;">
                        <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                        <button type="submit" onclick="return confirm('Are you sure you want to remove this ad from the queue?');">Remove</button>
                    </form>
                </td>
            </tr>
        <?php endwhile; ?>
    </table>
    <h3>Resting Advertisements</h3>
<table>
    <tr>
        <th>Title</th>
        <th>Price</th>
        <th>Description</th>
        <th>Actions</th>
    </tr>
    <?php foreach ($restingAds as $ad): ?>
        <tr>
            <td><?php echo htmlspecialchars($ad['title']); ?></td>
            <td>₹<?php echo htmlspecialchars($ad['price']); ?></td>
            <td><?php echo htmlspecialchars($ad['description']); ?></td>
            <td>
                <!-- Queue button -->
                <form action="up_que_status.php" method="POST" style="display:inline;">
                    <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                    <input type="hidden" name="is_queued" value="1">
                    <button type="submit">Queue</button>
                </form>

                <!-- Edit button -->
                <form action="edit_ad_by_admin.php" method="GET" style="display:inline;">
                    <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                    <button type="submit">Edit</button>
                </form>

                <!-- Delete button -->
                <form action="delete_resting_ad.php" method="POST" style="display:inline;">
                    <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                    <button type="submit" onclick="return confirm('Are you sure you want to delete this ad permanently?');">Delete</button>
                </form>
            </td>
        </tr>
    <?php endforeach; ?>
</table>
    
</div>
