create admin index page

index page should contain a sidebar
side bar should have

1. user management
	a. registered users list
	b. user registration approvel section-- when user creates account the admin must approve its account after that user can post ad.
	c. create buttons in registerd user list to delete user, make a user on hold-- when user is on hold he can not post ad, he can only brows and manage his ads.
	d. create a search user bar above all.
2. featured ad management
	a. admin will make which ad to feature in featured section
	b. ad will remain in featured section till 7 days
	c. inform admin on 6th day of any ad to update featured section.
3. adverstise section management
	a. add a upload section to create advertise on advertise section on public index page.
	b. it should have a title, category, decription price and image upload.
	c. a time limit to run ads ( how many days)
	d. a table to queue next add to run (give a form to queue new advertisement )
	f. form should contains title, category, decription price and image upload.
	e. when the timer runs out of current ad, the queued ad would post automatically.
	f. if there is no queued ad then make the section default.
4. category management
	a. admin can add or remove a category
5. reports
	a. users-- 	admin can see how many users are registed
			how many are active
			how many on hold
			how many are banned
	b. ads--	how many ads are posted
			how many ads are running
			
	