<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dairy Management System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
  <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
  <script
    src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
  <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000"
    data-border-radius="small"></script>
</head>

<body class="bg-gray-50 min-h-screen">
  <nav class="bg-white border-b border-gray-200">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16 items-center">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <img class="h-8 w-auto" src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png" alt="Logo" /><span
              class="ml-2 text-xl font-semibold text-gray-900"><a href="../index.php">DairyPro</a></span>
          </div>
          <div class="hidden md:flex md:ml-8 space-x-6">
            <a href="../cattle/cattle_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Cattle</a><a
              href="../feed/feed_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Feed</a><a
              href="../labour/labour_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Labour</a><a
              href="../production/production_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Production</a><a
              href="../sales/sales_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Sales</a>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            <span id="current-time"></span>
          </div>

        </div>
      </div>
    </div>
  </nav>
  <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-custom bg-opacity-10">
            <i class="fas fa-users text-custom"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Workers</p>
            <h3 class="text-xl font-semibold text-gray-900">156</h3>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100">
            <i class="fas fa-user-check text-green-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Salaried Workers</p>
            <h3 class="text-xl font-semibold text-gray-900">142</h3>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100">
            <i class="fas fa-user-shield text-red-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Daily Workers</p>
            <h3 class="text-xl font-semibold text-gray-900">8</h3>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100">
            <i class="fas fa-chart-pie text-yellow-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Attendance Rate</p>
            <h3 class="text-xl font-semibold text-gray-900">28.5%</h3>
          </div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mt-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-lg font-semibold text-gray-900">Mark Attendance</h2>
          <div class="flex space-x-4">
            <button class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium">
              Individual Check-in</button><button
              class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium">
              Bulk Check-in
            </button>
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Worker ID or Name</label><input type="text"
                class="w-full border-gray-300 rounded-md shadow-sm" placeholder="Enter ID or search by name" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Check-in Time</label><input type="time"
                class="w-full border-gray-300 rounded-md shadow-sm" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Check-out Time</label><input type="time"
                class="w-full border-gray-300 rounded-md shadow-sm" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label><textarea
                class="w-full border-gray-300 rounded-md shadow-sm" rows="3"
                placeholder="Add any additional notes"></textarea>
            </div>
          </div>
          <div class="space-y-4">
            <div class="p-4 bg-gray-50 rounded-lg">
              <h3 class="text-sm font-medium text-gray-900 mb-3">
                Worker Details
              </h3>
              <div class="space-y-2">
                <p class="text-sm text-gray-600">Name: John Smith</p>
                <p class="text-sm text-gray-600">
                  Worker Type: Salaried Worker
                </p>
                <p class="text-sm text-gray-600">Department: Production</p>
                <p class="text-sm text-gray-600">
                  Last Check-in: Today at 09:00 AM
                </p>
              </div>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
              <h3 class="text-sm font-medium text-gray-900 mb-3">
                Attendance Summary
              </h3>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <p class="text-xs text-gray-500">This Month</p>
                  <p class="text-lg font-semibold text-gray-900">22/23</p>
                </div>
                <div>
                  <p class="text-xs text-gray-500">Last Month</p>
                  <p class="text-lg font-semibold text-gray-900">30/31</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-6 flex justify-end space-x-4">
          <button class="!rounded-button text-gray-600 border border-gray-300 px-4 py-2 text-sm font-medium">
            Cancel</button><button class="!rounded-button bg-custom text-white px-6 py-2 text-sm font-medium">
            Submit Attendance
          </button>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 mt-6">
      <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-900">
            Attendance History
          </h2>
          <button class="!rounded-button text-custom border border-custom px-4 py-2 text-sm font-medium"
            onClick="window.location.href = 'attendance_history.php';">
            View All
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Date
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Worker Type
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Amount
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Status
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  2024-02-20
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  Salaried Worker
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  $2,450
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    Paid
                  </span>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  2024-02-19
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  Daily Worker
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  $5,800
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  2024-02-18
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  Salaried Worker
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  $780
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    Paid
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
          Worker Updates
        </h2>
        <div class="space-y-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
              </span>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Low stock alert</p>
              <p class="text-sm text-gray-500">
                Cheese inventory below threshold
              </p>
            </div>
          </div>
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-yellow-100">
                <i class="fas fa-clock text-yellow-600"></i>
              </span>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Expiring soon</p>
              <p class="text-sm text-gray-500">
                Yogurt batch expires in 3 days
              </p>
            </div>
          </div>
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-green-100">
                <i class="fas fa-check text-green-600"></i>
              </span>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Order completed</p>
              <p class="text-sm text-gray-500">Bulk order #12345 delivered</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
  <footer class="bg-white border-t border-gray-200 mt-8">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          Active Sessions: 45 | Last Backup: 2 hours ago
        </div>
        <div class="flex space-x-4">
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Reports
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Settings
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Support
          </button>
        </div>
      </div>
    </div>
  </footer>
  <script>
    function updateTime() {
      const now = new Date();
      document.getElementById("current-time").textContent =
        now.toLocaleString();
    }
    setInterval(updateTime, 1000);
    updateTime();
    const salesChart = echarts.init(document.getElementById("sales-chart"));
    const option = {
      animation: false,
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "#4F46E5",
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(79, 70, 229, 0.3)",
              },
              {
                offset: 1,
                color: "rgba(79, 70, 229, 0.1)",
              },
            ]),
          },
        },
      ],
    };
    salesChart.setOption(option);
    window.addEventListener("resize", function () {
      salesChart.resize();
    });
  </script>
</body>

</html>