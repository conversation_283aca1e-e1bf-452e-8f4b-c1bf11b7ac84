<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Feed Management - DairyPro</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
  <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
  <script
    src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
  <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000"
    data-border-radius="small"></script>
</head>

<body class="bg-gray-50 min-h-screen">
  <nav class="bg-white border-b border-gray-200">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16 items-center">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <img class="h-8 w-auto" src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png" alt="Logo" /><span
              class="ml-2 text-xl font-semibold text-gray-900"><a href="../index.php">DairyPro</a></span>
          </div>
          <div class="hidden md:flex md:ml-8 space-x-6">
            <a href="../cattle/cattle_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Cattle</a><a href="#"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Feed</a><a
              href="../labour/labour_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Labour</a><a
              href="../production/production_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Production</a><a
              href="../sales/sales_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Sales</a><a href="../reports.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Reporting</a>

          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            <span id="current-time"></span>
          </div>

        </div>
      </div>
    </div>
  </nav>
  <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow">
      <h1 class="text-2xl font-semibold mb-6">Add Feed Stock</h1>
      <form class="space-y-6">
        <div class="grid grid-cols-2 gap-6">
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Feed Type</label><select
              class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom">
              <option>Hay</option>
              <option>Grain Mix</option>
              <option>Silage</option>
              <option>Other</option>
            </select>
          </div>
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Quantity (kg)</label><input type="number"
              class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
              placeholder="Enter quantity" />
          </div>
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Supplier</label><select
              class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom">
              <option>FeedCo Ltd</option>
              <option>AgriFeeds</option>
              <option>Other</option>
            </select>
          </div>
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Unit Price ($)</label><input type="number"
              step="0.01" class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom"
              placeholder="Enter unit price" />
          </div>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">Notes</label><textarea
            class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom" rows="3"
            placeholder="Add any additional notes"></textarea>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">Delivery Date</label><input type="date"
            class="w-full border-gray-300 rounded-md shadow-sm focus:border-custom focus:ring-custom" />
        </div>
        <div class="flex justify-end space-x-4 pt-4">
          <button type="button"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom"
            onclick="window.history.back()">
            Cancel</button>
          <button type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-custom border border-transparent rounded-md shadow-sm hover:bg-custom-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom">
            Add Stock
          </button>
        </div>
      </form>
    </div>
  </main>
  <footer class="bg-white border-t border-gray-200 mt-8">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          Active Feed Orders: 2 | Pending Deliveries: 1
        </div>
        <div class="flex space-x-4">
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Reports
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Settings
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Support
          </button>
        </div>
      </div>
    </div>
  </footer>
  <script>
    function updateTime() {
      const now = new Date();
      document.getElementById("current-time").textContent =
        now.toLocaleString();
    }
    setInterval(updateTime, 1000);
    updateTime();
    const salesChart = echarts.init(document.getElementById("sales-chart"));
    const option = {
      animation: false,
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "#4F46E5",
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(79, 70, 229, 0.3)",
              },
              {
                offset: 1,
                color: "rgba(79, 70, 229, 0.1)",
              },
            ]),
          },
        },
      ],
    };
    salesChart.setOption(option);
    window.addEventListener("resize", function () {
      salesChart.resize();
    });
  </script>
</body>

</html>