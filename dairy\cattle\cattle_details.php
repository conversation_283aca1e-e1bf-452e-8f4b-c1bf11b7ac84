<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dairy Management System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
  <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
  <script
    src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
  <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000"
    data-border-radius="small"></script>
</head>

<body class="bg-gray-50 min-h-screen">
  <nav class="bg-white border-b border-gray-200">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16 items-center">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <img class="h-8 w-auto" src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png" alt="Logo" /><span
              class="ml-2 text-xl font-semibold text-gray-900"><a href="../index.php">DairyPro</a> </span>
          </div>
          <div class="hidden md:flex md:ml-8 space-x-6">
            <a href="../cattle/cattle_management.php"
              class="text-custom px-3 py-2 text-sm font-medium border-b-2 border-custom">Cattle</a>
            <a href="../feed/feed_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Feed</a>
            <a href="../labour/labour_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Labour</a>
            <a href="../Production/production_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Production</a>
            <a href="../sales/sales_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Sales</a>
            <a href="../Reports.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Reporting</a>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            <span id="current-time"></span>
          </div>

        </div>
      </div>
    </div>
  </nav>
  <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Cattle Management</h1>
      <button class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
        onclick="window.location.href='./add_cattle.php'">
        <i class="fas fa-plus mr-2"></i>Add New Cattle
      </button>
    </div>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-4">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-custom bg-opacity-10">
            <i class="fas fa-cow text-custom"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Cattle</p>
            <h3 class="text-xl font-semibold text-gray-900">156</h3>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100">
            <i class="fas fa-glass-whiskey text-green-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">
              Daily Milk Production
            </p>
            <h3 class="text-xl font-semibold text-gray-900">2,450 L</h3>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100">
            <i class="fas fa-stethoscope text-yellow-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Under Treatment</p>
            <h3 class="text-xl font-semibold text-gray-900">8</h3>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100">
            <i class="fas fa-calendar text-blue-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Pregnant</p>
            <h3 class="text-xl font-semibold text-gray-900">24</h3>
          </div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-6 mt-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex justify-between items-center mb-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-900">
              Cattle Details
            </h2>
            <button class="!rounded-button bg-custom text-white px-4 py-2 text-sm"
              onclick="window.location.href='./edit_cattle.php'">
              <i class="fas fa-edit mr-2"></i>Edit Cattle
            </button>
          </div>
        </div>
        <div class="overflow-x-auto">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg">
              <div class="flex items-start gap-4">
                <img src="https://ai-public.creatie.ai/gen_page/cattle_placeholder.jpg" alt="Cattle Image"
                  class="w-32 h-32 object-cover rounded-lg" />
                <div class="flex-1">
                  <h3 class="text-md font-semibold mb-4">
                    Basic Information
                  </h3>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <p class="text-sm text-gray-600">ID</p>
                      <p class="font-medium">CT001</p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-600">Name</p>
                      <p class="font-medium">Daisy</p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-600">Category</p>
                      <p class="font-medium">Milking</p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-600">Age</p>
                      <p class="font-medium">4 years</p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-600">Status</p>
                      <span
                        class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Healthy</span>
                    </div>
                    <div>
                      <p class="text-sm text-gray-600">Daily Milk</p>
                      <p class="font-medium">28 L</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-white p-6 rounded-lg">
              <h3 class="text-md font-semibold mb-4">Health Records</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-600">Last Checkup</p>
                  <p class="font-medium">2024-02-15</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Vaccination Status</p>
                  <p class="font-medium">Up to date</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Medical History</p>
                  <p class="text-sm text-gray-500">
                    No major health issues reported
                  </p>
                </div>
              </div>
            </div>
            <div class="bg-white p-6 rounded-lg">
              <h3 class="text-md font-semibold mb-4">Production History</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-600">
                    Average Daily Production
                  </p>
                  <p class="font-medium">28.5 L</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Peak Production</p>
                  <p class="font-medium">32 L</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Production Trend</p>
                  <div class="h-40" id="production-chart"></div>
                </div>
              </div>
            </div>
            <div class="bg-white p-6 rounded-lg">
              <h3 class="text-md font-semibold mb-4">Breeding Information</h3>
              <div class="space-y-4">
                <div>
                  <p class="text-sm text-gray-600">Breed</p>
                  <p class="font-medium">Holstein Friesian</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Last Calving Date</p>
                  <p class="font-medium">2023-08-10</p>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Number of Calves</p>
                  <p class="font-medium">2</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-4 flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Showing 1 to 3 of 156 entries
          </div>
          <div class="flex space-x-2">
            <button class="!rounded-button border border-gray-300 px-3 py-1 text-sm" disabled="">
              Previous</button><button
              class="!rounded-button border border-gray-300 px-3 py-1 text-sm bg-custom text-white">
              1</button><button class="!rounded-button border border-gray-300 px-3 py-1 text-sm">
              2</button><button class="!rounded-button border border-gray-300 px-3 py-1 text-sm">
              3</button><button class="!rounded-button border border-gray-300 px-3 py-1 text-sm">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
  <footer class="bg-white border-t border-gray-200 mt-8">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          Today&#39;s Transactions: 45 | Pending Orders: 12
        </div>
        <div class="flex space-x-4">
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Reports
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Settings
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Support
          </button>
        </div>
      </div>
    </div>
  </footer>
  <script>
    function updateTime() {
      const now = new Date();
      document.getElementById("current-time").textContent =
        now.toLocaleString();
    }
    setInterval(updateTime, 1000);
    updateTime();
    const salesChart = echarts.init(document.getElementById("sales-chart"));
    const option = {
      animation: false,
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "#4F46E5",
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(79, 70, 229, 0.3)",
              },
              {
                offset: 1,
                color: "rgba(79, 70, 229, 0.1)",
              },
            ]),
          },
        },
      ],
    };
    salesChart.setOption(option);
    window.addEventListener("resize", function () {
      salesChart.resize();
    });
  </script>
</body>

</html>