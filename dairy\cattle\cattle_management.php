<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000" data-border-radius="small"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
                ><a href="../index.php">DairyPro</a></span>
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Cattle</a
              ><a
                href="../feed/feed_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Feed</a
              ><a
                href="../labour/labour_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Labour</a
              ><a
                href="../production/production_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Production</a
              ><a
                href="../sales/sales_management.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Sales</a
              ><a
                href="../reports.php"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Reporting</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
          </div>
        </div>
      </div>
    </nav>

<main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="flex items-center mb-6 gap-4">
    <h1 class="text-2xl font-bold text-gray-900 mr-auto">Cattle Management</h1>
    <button
      onClick="window.location.href = 'add_cattle.php'"
      class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
    >
      <i class="fas fa-plus mr-2"></i>Add New Cattle</button
    ><button
      onClick="window.location.href = 'cattle_health.php'"
      class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
    >
      <i class="fas fa-syringe mr-2"></i>Vaccination Record
    </button>
  </div>
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-4">
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-custom bg-opacity-10">
          <i class="fas fa-cow text-custom"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Cattle</p>
          <h3 class="text-xl font-semibold text-gray-900">156</h3>
        </div>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100">
          <i class="fas fa-glass-whiskey text-green-600"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Daily Milk Production</p>
          <h3 class="text-xl font-semibold text-gray-900">2,450 L</h3>
        </div>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-yellow-100">
          <i class="fas fa-stethoscope text-yellow-600"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Under Treatment</p>
          <h3 class="text-xl font-semibold text-gray-900">8</h3>
        </div>
      </div>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-blue-100">
          <i class="fas fa-calendar text-blue-600"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Pregnant</p>
          <h3 class="text-xl font-semibold text-gray-900">24</h3>
        </div>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-1 gap-6 mt-6">
    <div class="bg-white p-6 rounded-lg shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-900">Cattle List</h2>
        <div class="flex space-x-2">
          <input
            type="text"
            placeholder="Search cattle..."
            class="border-gray-300 rounded-md text-sm"
          /><select class="border-gray-300 rounded-md text-sm">
            <option>All Categories</option>
            <option>Milking</option>
            <option>Dry</option>
            <option>Calves</option>
          </select>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                ID
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Name
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Category
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Age
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Status
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Daily Milk
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
              >
                Action
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                CT001
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Daisy
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Milking
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                4 years
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                  >Healthy</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                28 L
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
              <button
                  class="text-custom hover:text-custom-dark mr-2"
                  onclick="location.href='../cattle/edit_cattle.php'"
                >
                  <i class="fas fa-edit"></i>
              </button>
              <button 
                  class="text-custom hover:text-custom-dark"
                  onclick="location.href='./cattle_details.php'"
                  >
                  <i class="fas fa-eye"></i>
                </button>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                CT002
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Belle
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Dry
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                5 years
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800"
                  >Pregnant</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                0 L
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <button class="text-custom hover:text-custom-dark mr-2">
                  <i class="fas fa-edit"></i></button
                ><button class="text-custom hover:text-custom-dark">
                  <i class="fas fa-eye"></i>
                </button>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                CT003
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Luna
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                Milking
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                3 years
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800"
                  >Treatment</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                15 L
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <button class="text-custom hover:text-custom-dark mr-2">
                  <i class="fas fa-edit"></i></button
                ><button class="text-custom hover:text-custom-dark">
                  <i class="fas fa-eye"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-4 flex justify-between items-center">
        <div class="text-sm text-gray-500">Showing 1 to 3 of 156 entries</div>
        <div class="flex space-x-2">
          <button
            class="!rounded-button border border-gray-300 px-3 py-1 text-sm"
            disabled=""
          >
            Previous</button
          ><button
            class="!rounded-button border border-gray-300 px-3 py-1 text-sm bg-custom text-white"
          >
            1</button
          ><button
            class="!rounded-button border border-gray-300 px-3 py-1 text-sm"
          >
            2</button
          ><button
            class="!rounded-button border border-gray-300 px-3 py-1 text-sm"
          >
            3</button
          ><button
            class="!rounded-button border border-gray-300 px-3 py-1 text-sm"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</main>
<?php include('../include/footer.php'); ?>
