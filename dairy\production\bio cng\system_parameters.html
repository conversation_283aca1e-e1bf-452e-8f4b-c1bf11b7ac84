<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dairy Management System</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script
      src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js"
      data-color="#000000"
      data-border-radius="small"
    ></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <nav class="bg-white border-b border-gray-200">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img
                class="h-8 w-auto"
                src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png"
                alt="Logo"
              /><span class="ml-2 text-xl font-semibold text-gray-900"
                >DairyPro</span
              >
            </div>
            <div class="hidden md:flex md:ml-8 space-x-6">
              <a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Dashboard</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Feed</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Labour</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Production</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Sales</a
              ><a
                href="#"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
                >Reporting</a
              >
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span id="current-time"></span>
            </div>
            <button
              class="!rounded-button bg-custom text-white px-4 py-2 text-sm font-medium"
            >
              <i class="fas fa-plus mr-2"></i>Quick Add
            </button>
          </div>
        </div>
      </div>
    </nav>
    <div class="bg-gray-100 border-b border-gray-200" id="sub-menu-bar">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          class="flex items-center space-x-6 overflow-x-auto py-3 scrollbar-hide"
        >
          <a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Record by Cattle</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Milk</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Ghee</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Paneer</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Dahi</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Chhas</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Makhkhan</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Dung</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Urine</a
          ><a
            href="#"
            class="text-gray-600 hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Bio Fertilizer</a
          ><a
            href="#"
            class="text-custom hover:text-custom whitespace-nowrap px-3 py-2 text-sm font-medium"
            >Bio CNG</a
          >
        </div>
      </div>
    </div>
    <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="bg-white p-6 rounded-lg shadow mb-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-lg font-semibold text-gray-900">
            Edit System Parameters
          </h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Temperature (°C)</label
              ><input
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                value="37.5"
                step="0.1"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Pressure (bar)</label
              ><input
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                value="5.2"
                step="0.1"
              />
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >pH Level</label
              ><input
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                value="7.2"
                step="0.1"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Methane Content (%)</label
              ><input
                type="number"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                value="92"
                min="0"
                max="100"
              />
            </div>
          </div>
        </div>
        <div class="mt-6 flex justify-end space-x-4">
          <button
            class="!rounded-button px-4 py-2 text-sm font-medium border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel</button
          ><button
            class="!rounded-button px-4 py-2 text-sm font-medium bg-custom text-white hover:bg-custom/90"
          >
            Save Changes
          </button>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white p-6 rounded-lg shadow">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">
            Parameter History
          </h2>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Timestamp
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Temperature
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    Pressure
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                  >
                    pH
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    2024-01-20 14:30
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">37.5°C</td>
                  <td class="px-6 py-4 text-sm text-gray-900">5.2 bar</td>
                  <td class="px-6 py-4 text-sm text-gray-900">7.2</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">
            Alerts Configuration
          </h2>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Temperature Range</label
              >
              <div class="grid grid-cols-2 gap-4">
                <input
                  type="number"
                  placeholder="Min"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                  value="35"
                /><input
                  type="number"
                  placeholder="Max"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom focus:border-custom"
                  value="40"
                />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700"
                >Alert Notifications</label
              >
              <div class="mt-2">
                <label class="inline-flex items-center"
                  ><input
                    type="checkbox"
                    class="rounded border-gray-300 text-custom focus:ring-custom"
                    checked=""
                  /><span class="ml-2 text-sm text-gray-600">Email</span></label
                ><label class="inline-flex items-center ml-6"
                  ><input
                    type="checkbox"
                    class="rounded border-gray-300 text-custom focus:ring-custom"
                    checked=""
                  /><span class="ml-2 text-sm text-gray-600">SMS</span></label
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Today&#39;s Transactions: 45 | Pending Orders: 12
          </div>
          <div class="flex space-x-4">
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Reports
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Settings
            </button>
            <button
              class="!rounded-button text-gray-600 hover:text-gray-900 text-sm"
            >
              Support
            </button>
          </div>
        </div>
      </div>
    </footer>
    <script>
      function updateTime() {
        const now = new Date();
        document.getElementById("current-time").textContent =
          now.toLocaleString();
      }
      setInterval(updateTime, 1000);
      updateTime();
      const salesChart = echarts.init(document.getElementById("sales-chart"));
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#4F46E5",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(79, 70, 229, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(79, 70, 229, 0.1)",
                },
              ]),
            },
          },
        ],
      };
      salesChart.setOption(option);
      window.addEventListener("resize", function () {
        salesChart.resize();
      });
    </script>
  </body>
</html>
