"use client";

import type React from "react";
import Link from "next/link";
import { redirect } from "next/navigation";
import {
  BarChart3,
  Package,
  ShoppingCart,
  Star,
  Users,
  LogOut,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { useAuth } from "@/app/context/AuthContext";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isAdmin, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-emerald-600 border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated or not admin
  if (!user || !isAdmin) {
    redirect("/signin");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TooltipProvider>
        <div className="flex">
          {/* Sidebar */}
          <aside className="fixed inset-y-0 z-50 flex h-full w-16 flex-col border-r bg-white shadow-sm md:w-64">
            <div className="border-b px-3 py-4">
              <Link href="/admin" className="flex items-center gap-2">
                <span className="sr-only md:not-sr-only md:text-xl md:font-bold text-emerald-600">
                  Admin Panel
                </span>
              </Link>
            </div>
            <nav className="flex-1 space-y-1 p-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href="/admin">
                    <Button
                      variant="ghost"
                      className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium"
                    >
                      <BarChart3 className="h-5 w-5 text-gray-500" />
                      <span className="sr-only md:not-sr-only">Dashboard</span>
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  Dashboard
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href="/admin/users">
                    <Button
                      variant="ghost"
                      className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium"
                    >
                      <Users className="h-5 w-5 text-gray-500" />
                      <span className="sr-only md:not-sr-only">Users</span>
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  Users
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href="/admin/products">
                    <Button
                      variant="ghost"
                      className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium"
                    >
                      <Package className="h-5 w-5 text-gray-500" />
                      <span className="sr-only md:not-sr-only">Products</span>
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  Products
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href="/admin/featured">
                    <Button
                      variant="ghost"
                      className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium"
                    >
                      <Star className="h-5 w-5 text-gray-500" />
                      <span className="sr-only md:not-sr-only">
                        Featured Products
                      </span>
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  Featured Products
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href="/admin/orders">
                    <Button
                      variant="ghost"
                      className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium"
                    >
                      <ShoppingCart className="h-5 w-5 text-gray-500" />
                      <span className="sr-only md:not-sr-only">Orders</span>
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  Orders
                </TooltipContent>
              </Tooltip>
            </nav>
            <div className="border-t p-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link href="/">
                    <Button
                      variant="ghost"
                      className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium"
                    >
                      <span className="sr-only md:not-sr-only">View Site</span>
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  View Site
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700"
                    onClick={() => {
                      // Add logout functionality here
                    }}
                  >
                    <LogOut className="h-5 w-5" />
                    <span className="sr-only md:not-sr-only">Logout</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right" className="md:hidden">
                  Logout
                </TooltipContent>
              </Tooltip>
            </div>
          </aside>

          {/* Main content */}
          <main className="flex-1 pl-16 md:pl-64">
            <div className="container mx-auto p-6 md:p-8">{children}</div>
          </main>
        </div>
      </TooltipProvider>
    </div>
  );
}
