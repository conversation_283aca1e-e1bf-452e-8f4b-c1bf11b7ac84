"use client"

import { useState } from "react"
import Link from "next/link"
import { Eye, MoreHorizontal } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

// Sample order data
const initialOrders = [
  {
    id: "3210",
    customer: "<PERSON>",
    email: "<EMAIL>",
    status: "shipped",
    date: "2023-04-01T10:30:00Z",
    total: 42.25,
    items: 2,
  },
  {
    id: "3209",
    customer: "<PERSON>",
    email: "<EMAIL>",
    status: "paid",
    date: "2023-03-30T14:20:00Z",
    total: 74.99,
    items: 1,
  },
  {
    id: "3208",
    customer: "<PERSON>",
    email: "<EMAIL>",
    status: "processing",
    date: "2023-03-29T09:15:00Z",
    total: 64.75,
    items: 3,
  },
  {
    id: "3207",
    customer: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    status: "paid",
    date: "2023-03-28T11:45:00Z",
    total: 99.99,
    items: 1,
  },
  {
    id: "3206",
    customer: "<PERSON>",
    email: "<EMAIL>",
    status: "shipped",
    date: "2023-03-27T16:10:00Z",
    total: 67.5,
    items: 2,
  },
  {
    id: "3205",
    customer: "Lisa Anderson",
    email: "<EMAIL>",
    status: "delivered",
    date: "2023-03-25T13:30:00Z",
    total: 34.5,
    items: 1,
  },
  {
    id: "3204",
    customer: "Samantha Green",
    email: "<EMAIL>",
    status: "cancelled",
    date: "2023-03-24T10:20:00Z",
    total: 89.99,
    items: 4,
  },
]

export function OrdersTable() {
  const [orders] = useState(initialOrders)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "paid":
        return "outline"
      case "processing":
        return "secondary"
      case "shipped":
        return "default"
      case "delivered":
        return "success"
      case "cancelled":
        return "destructive"
      default:
        return "outline"
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Items</TableHead>
            <TableHead>Total</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => (
            <TableRow key={order.id}>
              <TableCell className="font-medium">#{order.id}</TableCell>
              <TableCell>
                <div>
                  <p>{order.customer}</p>
                  <p className="text-sm text-muted-foreground">{order.email}</p>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={getStatusBadgeVariant(order.status)}>{order.status}</Badge>
              </TableCell>
              <TableCell>{formatDate(order.date)}</TableCell>
              <TableCell>{order.items}</TableCell>
              <TableCell>${order.total.toFixed(2)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href={`/admin/orders/${order.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem>Update Status</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

