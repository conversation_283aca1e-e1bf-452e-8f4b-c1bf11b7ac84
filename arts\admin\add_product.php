<?php
// add_product.php

include('config.php');

// Fetch categories for dropdown
$categoryStmt = $pdo->prepare("SELECT * FROM categories");
$categoryStmt->execute();
$categories = $categoryStmt->fetchAll();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = $_POST['name'];
    $price = $_POST['price'];
    $category_id = $_POST['category'];
    $image = $_FILES['image']['name'];
    
    // Handle file upload
    if ($image) {
        $target_dir = "uploads/";
        $target_file = $target_dir . basename($image);
        move_uploaded_file($_FILES['image']['tmp_name'], $target_file);
    }

    // Insert product into database
    $stmt = $pdo->prepare("INSERT INTO products (name, price, category_id, image) VALUES (?, ?, ?, ?)");
    $stmt->execute([$name, $price, $category_id, $image]);

    // Redirect to products page
    header('Location: products.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Product</title>
    <link rel="stylesheet" href="../styles.css">
</head>
<body>
    <!-- Header Section -->
    <?php
    session_start();
    if (!isset($_SESSION['logged_in'])) {
        header('Location: login.php');
        exit;
    }

    echo '<h1>Admin Panel</h1>';
    echo '<a href="categories.php">Manage Categories</a> | ';
    echo '<a href="products.php">Manage Products</a> | ';
    echo '<a href="logout.php">Logout</a>';
    ?>

    <!-- Add Product Form -->
    <section class="add-product">
        <h2>Add New Product</h2>
        <form action="add_product.php" method="post" enctype="multipart/form-data">
            <label for="name">Product Name:</label>
            <input type="text" name="name" required>

            <label for="price">Price:</label>
            <input type="number" name="price" step="0.01" required>

            <label for="category">Category:</label>
            <select name="category" required>
                <option value="">Select Category</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                <?php endforeach; ?>
            </select>

            <label for="image">Product Image:</label>
            <input type="file" name="image" required>

            <button type="submit">Add Product</button>
        </form>
    </section>

    <!-- Footer Section -->
    <footer>
        <p>&copy; 2024 My E-commerce Site. All rights reserved.</p>
    </footer>

    <script src="scripts.js"></script>
</body>
</html>
