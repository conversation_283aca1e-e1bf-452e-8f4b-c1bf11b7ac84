<?php
// products.php

include('config.php');

// Pagination settings
$limit = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

// Search settings
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Fetch total products
$totalStmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE name LIKE ?");
$totalStmt->execute(["%$search%"]);
$total = $totalStmt->fetchColumn();

// Fetch products with pagination
$productsStmt = $pdo->prepare("SELECT * FROM products WHERE name LIKE ? LIMIT $limit OFFSET $offset");
$productsStmt->execute(["%$search%"]);
$products = $productsStmt->fetchAll();

// Calculate total pages
$totalPages = ceil($total / $limit);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products</title>
    <link rel="stylesheet" href="../styles.css">
</head>
<body>
    <!-- Header Section -->
    <?php
    session_start();
    if (!isset($_SESSION['logged_in'])) {
        header('Location: login.php');
        exit;
    }

    echo '<h1>Admin Panel</h1>';
    echo '<a href="categories.php">Manage Categories</a> | ';
    echo '<a href="products.php">Manage Products</a> | ';
    echo '<a href="logout.php">Logout</a>';
    ?>

    <!-- Add New Product Button -->
    <section class="add-new-product">
        <a href="add_product.php" class="btn">Add New Product</a>
    </section>

    <!-- Search Form -->
    <section class="search">
        <form action="products.php" method="get">
            <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search products...">
            <button type="submit">Search</button>
        </form>
    </section>

    <!-- Product Listing -->
    <section class="product-listing">
    <h2>Manage Products</h2>
    <a href="add_product.php" class="add-product-button">Add New Product</a>
    <div class="product-grid">
        <?php foreach ($products as $product): ?>
            <div class="product-item">
                <img src="uploads/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                <p>$<?php echo number_format($product['price'], 2); ?></p>
                <a href="edit_product.php?id=<?php echo $product['id']; ?>">Edit</a>
                <a href="delete_product.php?id=<?php echo $product['id']; ?>" onclick="return confirm('Are you sure?')">Delete</a>
                <?php if ($product['is_featured'] == 0): ?>
                    <a href="feature_product.php?id=<?php echo $product['id']; ?>">Feature</a>
                <?php else: ?>
                    <span>Featured</span>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>

        <!-- Pagination -->
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="products.php?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>">Previous</a>
            <?php endif; ?>

            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                <a href="products.php?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
            <?php endfor; ?>

            <?php if ($page < $totalPages): ?>
                <a href="products.php?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>">Next</a>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer Section -->
    <footer>
        <p>&copy; 2024 My E-commerce Site. All rights reserved.</p>
    </footer>

    <script src="scripts.js"></script>
</body>
</html>
