import { useState, useEffect } from "react";

// MicroWorksApp - single-file React component (Tailwind CSS assumed)
// Purpose: interactive front-end prototype for a micro-tasks marketplace
// Notes for developer:
//  - Tailwind classes are used. Ensure Tailwind is enabled in the project.
//  - Replace placeholder payment handlers with real integration (Razorpay / PayPal / Stripe).
//  - Implement backend endpoints for orders, user auth, KYC, anti-fraud and delivery reporting.
//  - This demo intentionally does NOT include any automation instructions that would violate platform ToS.

const SERVICES = [
  {
    id: "caption_writing",
    title: "Caption Writing & Hashtag Research",
    unitLabel: "per post",
    baseINR: 25,
    baseUSD: 0.5,
    note: "Creative captions with strategic hashtag research",
  },
  {
    id: "graphic_design",
    title: "Graphic Design (Social Posts, Thumbnails)",
    unitLabel: "per design",
    baseINR: 75,
    baseUSD: 1.5,
  },
  {
    id: "transcription",
    title: "Transcription Services",
    unitLabel: "per minute",
    baseINR: 15,
    baseUSD: 0.3,
  },
  {
    id: "translation",
    title: "Translation Services",
    unitLabel: "per 100 words",
    baseINR: 50,
    baseUSD: 1,
  },
  {
    id: "video_editing",
    title: "Short Video Editing",
    unitLabel: "per video",
    baseINR: 150,
    baseUSD: 3,
  },
  {
    id: "voiceover",
    title: "Professional Voiceovers",
    unitLabel: "per clip",
    baseINR: 100,
    baseUSD: 2,
  },
  {
    id: "data_entry",
    title: "Data Entry & Excel Formatting",
    unitLabel: "per 100 rows",
    baseINR: 30,
    baseUSD: 0.6,
  },
  {
    id: "research_tasks",
    title: "Research Tasks (Leads, Info Collection)",
    unitLabel: "per task",
    baseINR: 80,
    baseUSD: 1.5,
  },
  {
    id: "profile_audit",
    title: "Profile Audits & Growth Strategies",
    unitLabel: "per audit",
    baseINR: 200,
    baseUSD: 4,
  },
  {
    id: "content_scheduling",
    title: "Social Media Content Scheduling",
    unitLabel: "per week",
    baseINR: 120,
    baseUSD: 2.5,
    note: "With proper authorization and permissions",
  },
];

// Helper functions for enhanced UI
function getServiceIcon(serviceId) {
  const icons = {
    caption_writing: "✍️",
    graphic_design: "🎨",
    transcription: "📝",
    translation: "🌐",
    video_editing: "🎬",
    voiceover: "🎤",
    data_entry: "📊",
    research_tasks: "🔍",
    profile_audit: "📋",
    content_scheduling: "📅",
  };
  return icons[serviceId] || "⚡";
}

function getServiceDescription(serviceId) {
  const descriptions = {
    caption_writing:
      "Creative and engaging captions with strategic hashtag research to maximize your post reach and engagement.",
    graphic_design:
      "Professional graphic design for social media posts, YouTube thumbnails, and marketing materials.",
    transcription:
      "Accurate transcription services for audio and video content with quick turnaround and high quality.",
    translation:
      "Professional translation services for documents, content, and communications in multiple languages.",
    video_editing:
      "Expert short video editing for social media, promotional content, and marketing videos.",
    voiceover:
      "Professional voiceover recordings for videos, advertisements, presentations, and multimedia content.",
    data_entry:
      "Reliable data entry, Excel formatting, and spreadsheet organization services with attention to detail.",
    research_tasks:
      "Comprehensive research services including lead generation, market research, and information collection.",
    profile_audit:
      "In-depth social media profile analysis with actionable growth strategies and optimization recommendations.",
    content_scheduling:
      "Professional social media content scheduling and management services with proper authorization.",
  };
  return (
    descriptions[serviceId] || "Professional micro-service with fast delivery."
  );
}

function getDeliveryTime(serviceId) {
  const times = {
    caption_writing: "Same day",
    graphic_design: "1-2 days",
    transcription: "Same day",
    translation: "1-2 days",
    video_editing: "2-3 days",
    voiceover: "2-3 days",
    data_entry: "1-2 days",
    research_tasks: "2-4 days",
    profile_audit: "2-3 days",
    content_scheduling: "1-2 days",
  };
  return times[serviceId] || "1-3 days";
}

export default function App() {
  const [currency, setCurrency] = useState(
    () => localStorage.getItem("microworks-currency") || "INR"
  );
  const [country, setCountry] = useState(
    () => localStorage.getItem("microworks-country") || "India"
  );
  const [cart, setCart] = useState(() => {
    const saved = localStorage.getItem("microworks-cart");
    return saved ? JSON.parse(saved) : [];
  });
  const [selectedService, setSelectedService] = useState(null);
  const [orderModalOpen, setOrderModalOpen] = useState(false);
  const [tosOpen, setTosOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [favorites, setFavorites] = useState(() => {
    const saved = localStorage.getItem("microworks-favorites");
    return saved ? JSON.parse(saved) : [];
  });
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);

  // Save to localStorage when state changes
  useEffect(() => {
    localStorage.setItem("microworks-currency", currency);
  }, [currency]);

  useEffect(() => {
    localStorage.setItem("microworks-country", country);
  }, [country]);

  useEffect(() => {
    localStorage.setItem("microworks-cart", JSON.stringify(cart));
  }, [cart]);

  useEffect(() => {
    localStorage.setItem("microworks-favorites", JSON.stringify(favorites));
  }, [favorites]);

  // Service categories
  const categories = [
    { id: "all", name: "All Services", icon: "🌟" },
    { id: "content", name: "Content Creation", icon: "🎨" },
    { id: "data", name: "Data & Research", icon: "📊" },
    { id: "marketing", name: "Marketing Services", icon: "📱" },
  ];

  // Categorize services
  const serviceCategories = {
    content: [
      "caption_writing",
      "graphic_design",
      "video_editing",
      "voiceover",
    ],
    data: ["transcription", "translation", "data_entry", "research_tasks"],
    marketing: ["profile_audit", "content_scheduling"],
  };

  // Filter services based on search and category
  const filteredServices = SERVICES.filter((service) => {
    const matchesSearch =
      service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.unitLabel.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategory === "all" ||
      serviceCategories[selectedCategory]?.includes(service.id);

    return matchesSearch && matchesCategory;
  });

  function addToCart(service, quantity, extra = {}) {
    const item = { ...service, quantity, extra };
    setCart((prev) => [...prev, item]);
    setOrderModalOpen(false);

    // Add notification
    addNotification(`Added ${service.title} to cart`, "success");
  }

  function addNotification(message, type = "info") {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date(),
    };
    setNotifications((prev) => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications

    // Auto remove after 5 seconds
    setTimeout(() => {
      setNotifications((prev) => prev.filter((n) => n.id !== notification.id));
    }, 5000);
  }

  function toggleFavorite(serviceId) {
    setFavorites((prev) =>
      prev.includes(serviceId)
        ? prev.filter((id) => id !== serviceId)
        : [...prev, serviceId]
    );
  }

  function removeFromCart(idx) {
    setCart((prev) => prev.filter((_, i) => i !== idx));
  }

  function updateCartQuantity(idx, newQuantity) {
    if (newQuantity < 1) return;
    setCart((prev) =>
      prev.map((item, i) =>
        i === idx ? { ...item, quantity: newQuantity } : item
      )
    );
  }

  function subtotal() {
    let s = 0;
    for (const it of cart) {
      s += (currency === "USD" ? it.baseUSD : it.baseINR) * it.quantity;
    }
    return currency === "USD" ? s.toFixed(2) : s.toFixed(0);
  }

  function placeOrder() {
    // Placeholder: This should call your backend to create an order and redirect to payment.
    // Important backend requirements: user authentication, KYC for sellers/buyers, fraud checks,
    // delivery webhook from workers, dispute mechanism.
    const orderTotal = currency === "INR" ? `₹${subtotal()}` : `$${subtotal()}`;
    addNotification(
      `Order placed successfully! Total: ${orderTotal}`,
      "success"
    );
    setCart([]);
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 sm:h-16">
            <div className="flex items-center space-x-2 sm:space-x-4">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-sm sm:text-lg">
                    MW
                  </span>
                </div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  MicroWorks
                </h1>
                <p className="text-xs text-gray-500">
                  Professional micro-services
                </p>
              </div>
              <div className="sm:hidden">
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  MW
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-1 sm:space-x-3">
              <select
                value={country}
                onChange={(e) => setCountry(e.target.value)}
                className="hidden sm:block bg-white border border-gray-200 rounded-lg px-2 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                <option>🇮🇳 India</option>
                <option>🇺🇸 United States</option>
                <option>🇬🇧 United Kingdom</option>
                <option>🌍 Other</option>
              </select>
              <select
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                className="bg-white border border-gray-200 rounded-lg px-2 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                <option value="INR">₹</option>
                <option value="USD">$</option>
              </select>
              <div className="relative">
                <button
                  className="relative bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 sm:px-3 sm:py-2 rounded-lg text-sm font-medium transition-all duration-200 mr-1 sm:mr-3"
                  onClick={() => setShowNotifications(!showNotifications)}
                >
                  <svg
                    className="w-4 h-4 sm:w-5 sm:h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v4"
                    />
                  </svg>
                  {notifications.length > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {notifications.length}
                    </span>
                  )}
                </button>

                {/* Notifications Dropdown */}
                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50">
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="font-semibold text-gray-900">
                        Notifications
                      </h3>
                    </div>
                    <div className="max-h-64 overflow-y-auto">
                      {notifications.length === 0 ? (
                        <div className="p-4 text-center text-gray-500 text-sm">
                          No notifications yet
                        </div>
                      ) : (
                        notifications.map((notification) => (
                          <div
                            key={notification.id}
                            className="p-4 border-b border-gray-100 last:border-b-0"
                          >
                            <div className="flex items-start space-x-3">
                              <div
                                className={`w-2 h-2 rounded-full mt-2 ${
                                  notification.type === "success"
                                    ? "bg-green-500"
                                    : notification.type === "error"
                                    ? "bg-red-500"
                                    : "bg-blue-500"
                                }`}
                              ></div>
                              <div className="flex-1">
                                <p className="text-sm text-gray-900">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  {notification.timestamp.toLocaleTimeString()}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </div>

              <button
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-2 sm:px-4 py-1 sm:py-2 rounded-lg text-xs sm:text-sm font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                onClick={() => setTosOpen(true)}
              >
                <span className="hidden sm:inline">Safety & Terms</span>
                <span className="sm:hidden">Terms</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Small Tasks,{" "}
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Real Results
            </span>
          </h2>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-2xl mx-auto mb-6 sm:mb-8 px-4">
            Professional micro-services for social media growth, content
            creation, and digital tasks. Fast delivery, honest pricing, real
            results.
          </p>

          {/* Search and Filter Section */}
          <div className="max-w-4xl mx-auto px-2">
            <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-lg border border-gray-200">
              <div className="flex flex-col gap-3 sm:gap-4">
                {/* Search Bar */}
                <div className="w-full relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search services..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full pl-9 sm:pl-10 pr-3 py-2 sm:py-3 border border-gray-200 rounded-xl leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm sm:text-base"
                  />
                </div>

                {/* Category Filter */}
                <div className="flex space-x-2 overflow-x-auto pb-2 scrollbar-hide">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-4 py-2 rounded-xl text-xs sm:text-sm font-medium transition-all whitespace-nowrap flex-shrink-0 ${
                        selectedCategory === category.id
                          ? "bg-blue-600 text-white shadow-lg"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                    >
                      <span className="text-sm sm:text-base">
                        {category.icon}
                      </span>
                      <span className="hidden sm:inline">{category.name}</span>
                      <span className="sm:hidden">
                        {category.name.split(" ")[0]}
                      </span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Results Count */}
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Showing{" "}
                  <span className="font-semibold text-blue-600">
                    {filteredServices.length}
                  </span>{" "}
                  of {SERVICES.length} services
                  {searchQuery && (
                    <span>
                      {" "}
                      for "<span className="font-semibold">{searchQuery}</span>"
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-8">
          {/* Services Grid */}
          <section className="lg:col-span-3">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-2">
              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">
                {selectedCategory === "all"
                  ? "All Services"
                  : categories.find((c) => c.id === selectedCategory)?.name}
              </h3>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSearchQuery("")}
                  className="text-xs sm:text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  Clear Search
                </button>
              </div>
            </div>

            {filteredServices.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-12 h-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No services found
                </h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search or filter criteria
                </p>
                <button
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategory("all");
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Show All Services
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                {filteredServices.map((s) => (
                  <article
                    key={s.id}
                    className="group bg-white rounded-2xl p-4 sm:p-6 shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-blue-200 hover:-translate-y-1"
                  >
                    {/* Service Icon and Favorite */}
                    <div className="flex items-center justify-between mb-3 sm:mb-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <span className="text-white font-bold text-base sm:text-lg">
                          {getServiceIcon(s.id)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <button
                          onClick={() => toggleFavorite(s.id)}
                          className={`p-1.5 sm:p-2 rounded-full transition-all duration-200 ${
                            favorites.includes(s.id)
                              ? "bg-red-100 text-red-600 hover:bg-red-200"
                              : "bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-red-500"
                          }`}
                        >
                          <svg
                            className="w-3 h-3 sm:w-4 sm:h-4"
                            fill={
                              favorites.includes(s.id) ? "currentColor" : "none"
                            }
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                        <div className="hidden sm:flex items-center space-x-1">
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-current"
                              viewBox="0 0 20 20"
                            >
                              <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                            </svg>
                          ))}
                          <span className="text-xs text-gray-500 ml-1">
                            (4.8)
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Service Content */}
                    <h3 className="font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors text-sm sm:text-base">
                      {s.title}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-600 mb-1">
                      {s.unitLabel}
                    </p>
                    <p className="text-xs text-gray-500 mb-3 sm:mb-4 line-clamp-2 hidden sm:block">
                      {getServiceDescription(s.id)}
                    </p>

                    {/* Delivery Time */}
                    <div className="flex items-center mb-3 sm:mb-4">
                      <svg
                        className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-1 sm:mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <span className="text-xs text-green-600 font-medium">
                        {getDeliveryTime(s.id)}
                      </span>
                    </div>

                    {/* Price and Actions */}
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-lg sm:text-xl font-bold text-gray-900">
                          {currency === "INR"
                            ? `₹${s.baseINR}`
                            : `$${s.baseUSD}`}
                        </div>
                        <div className="text-xs text-gray-500">
                          starting price
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
                          onClick={() => {
                            setSelectedService(s);
                            setOrderModalOpen(true);
                          }}
                        >
                          <span className="hidden sm:inline">Order Now</span>
                          <span className="sm:hidden">Order</span>
                        </button>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            )}
          </section>

          {/* Shopping Cart */}
          <aside className="lg:col-span-1 order-first lg:order-last">
            <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-sm border border-gray-100 lg:sticky lg:top-24">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <h4 className="text-base sm:text-lg font-bold text-gray-900">
                  <span className="hidden sm:inline">Shopping Cart</span>
                  <span className="sm:hidden">Cart</span>
                </h4>
                <div className="bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">
                  {cart.length} {cart.length === 1 ? "item" : "items"}
                </div>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {cart.length === 0 && (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15.5M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-500 text-sm">Your cart is empty</p>
                    <p className="text-gray-400 text-xs mt-1">
                      Add services to get started
                    </p>
                  </div>
                )}

                {cart.map((it, idx) => (
                  <div
                    key={idx}
                    className="bg-gray-50 rounded-xl p-3 sm:p-4 border border-gray-100"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1 min-w-0">
                        <h5 className="font-medium text-gray-900 text-xs sm:text-sm leading-tight truncate">
                          {it.title}
                        </h5>
                        <p className="text-xs text-gray-500 mt-1">
                          {it.quantity} × {it.unitLabel}
                        </p>
                        {it.extra?.details && (
                          <p className="text-xs text-blue-600 mt-1 truncate">
                            Target: {it.extra.details}
                          </p>
                        )}
                      </div>
                      <button
                        className="text-red-400 hover:text-red-600 transition-colors ml-2 flex-shrink-0"
                        onClick={() => removeFromCart(idx)}
                      >
                        <svg
                          className="w-3 h-3 sm:w-4 sm:h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <button
                          className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50"
                          onClick={() =>
                            updateCartQuantity(
                              idx,
                              Math.max(1, it.quantity - 1)
                            )
                          }
                        >
                          <svg
                            className="w-2 h-2 sm:w-3 sm:h-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M20 12H4"
                            />
                          </svg>
                        </button>
                        <span className="text-xs sm:text-sm font-medium text-gray-900 min-w-[16px] sm:min-w-[20px] text-center">
                          {it.quantity}
                        </span>
                        <button
                          className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50"
                          onClick={() =>
                            updateCartQuantity(idx, it.quantity + 1)
                          }
                        >
                          <svg
                            className="w-3 h-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 4v16m8-8H4"
                            />
                          </svg>
                        </button>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-900">
                          {currency === "INR"
                            ? `₹${it.baseINR * it.quantity}`
                            : `$${(it.baseUSD * it.quantity).toFixed(2)}`}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {cart.length > 0 && (
                <div className="border-t border-gray-200 mt-6 pt-6">
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">
                        {currency === "INR"
                          ? `₹${subtotal()}`
                          : `$${subtotal()}`}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Service fee</span>
                      <span className="font-medium">
                        {currency === "INR" ? "₹0" : "$0"}
                      </span>
                    </div>
                    <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                      <span>Total</span>
                      <span className="text-blue-600">
                        {currency === "INR"
                          ? `₹${subtotal()}`
                          : `$${subtotal()}`}
                      </span>
                    </div>
                  </div>

                  <button
                    onClick={placeOrder}
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 rounded-xl font-medium hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Proceed to Checkout
                  </button>
                </div>
              )}

              <div className="mt-6 p-4 bg-blue-50 rounded-xl">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg
                      className="w-3 h-3 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-blue-900 mb-1">
                      Pricing Guide
                    </p>
                    <p className="text-xs text-blue-700 leading-relaxed">
                      All prices are base rates. Final costs may vary based on
                      delivery time, complexity, and verification requirements.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>

      {/* Order modal */}
      {orderModalOpen && selectedService && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl w-full max-w-lg p-6 shadow-2xl transform transition-all">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  Order Service
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  {selectedService.title}
                </p>
              </div>
              <button
                onClick={() => setOrderModalOpen(false)}
                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
              >
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <OrderForm
              service={selectedService}
              currency={currency}
              onCancel={() => setOrderModalOpen(false)}
              onAdd={(q, extra) => addToCart(selectedService, q, extra)}
            />
          </div>
        </div>
      )}

      {/* TOS modal */}
      {tosOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl w-full max-w-2xl p-8 shadow-2xl transform transition-all max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Terms of Service & Safety
              </h3>
              <button
                onClick={() => setTosOpen(false)}
                className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
              >
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="space-y-6">
              <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-4 h-4 text-red-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-900 mb-2">
                      Important Safety Notice
                    </h4>
                    <p className="text-sm text-red-800 leading-relaxed">
                      By using MicroWorks, you agree that all orders will follow
                      platform rules and local laws. We strictly prohibit
                      fraudulent or abusive actions including fake accounts
                      created by automation, harassment, hate speech, doxxing,
                      or any illegal activities.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-blue-50 rounded-xl p-4">
                  <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    Quality Assurance
                  </h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• All services from verified providers</li>
                    <li>• Real accounts and genuine engagement</li>
                    <li>• Quality monitoring and reviews</li>
                    <li>• Satisfaction guarantee</li>
                  </ul>
                </div>

                <div className="bg-green-50 rounded-xl p-4">
                  <h4 className="font-semibold text-green-900 mb-3 flex items-center">
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                    Security & Privacy
                  </h4>
                  <ul className="text-sm text-green-800 space-y-1">
                    <li>• Secure payment processing</li>
                    <li>• Data protection compliance</li>
                    <li>• KYC verification when required</li>
                    <li>• Anti-fraud monitoring</li>
                  </ul>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-4">
                <h4 className="font-semibold text-gray-900 mb-3">
                  Seller & Buyer Responsibilities
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">
                      Sellers must:
                    </h5>
                    <ul className="space-y-1">
                      <li>• Provide honest delivery reports</li>
                      <li>• Complete orders within timeframe</li>
                      <li>• Maintain service quality standards</li>
                      <li>• Follow platform guidelines</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">
                      Buyers must:
                    </h5>
                    <ul className="space-y-1">
                      <li>• Provide accurate order details</li>
                      <li>• Make timely payments</li>
                      <li>• Respect seller terms</li>
                      <li>• Use services responsibly</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8 flex justify-end">
              <button
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                onClick={() => setTosOpen(false)}
              >
                I Understand
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">MW</span>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    MicroWorks
                  </h3>
                  <p className="text-sm text-gray-500">
                    Professional micro-services
                  </p>
                </div>
              </div>
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                Your trusted platform for high-quality micro-services. From
                social media growth to content creation, we connect you with
                verified professionals who deliver real results.
              </p>
              <div className="flex space-x-4">
                <a
                  href="#"
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                  </svg>
                </a>
                <a
                  href="#"
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z" />
                  </svg>
                </a>
                <a
                  href="#"
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </a>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Social Media
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Content Creation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Data Services
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Digital Marketing
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Safety Guidelines
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-blue-600 transition-colors">
                    Report Issue
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-500">
              © 2024 MicroWorks. All rights reserved. This is a prototype for
              demonstration purposes.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a
                href="#"
                className="text-sm text-gray-500 hover:text-blue-600 transition-colors"
              >
                Privacy Policy
              </a>
              <a
                href="#"
                className="text-sm text-gray-500 hover:text-blue-600 transition-colors"
              >
                Terms of Service
              </a>
              <a
                href="#"
                className="text-sm text-gray-500 hover:text-blue-600 transition-colors"
              >
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

function OrderForm({ service, currency, onCancel, onAdd }) {
  const [quantity, setQuantity] = useState(1);
  const [details, setDetails] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  function unitLabel() {
    return service.unitLabel;
  }

  const handleAddToCart = async () => {
    setIsLoading(true);
    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 500));
    onAdd(quantity, { details });
    setIsLoading(false);
  };

  return (
    <div className="space-y-6">
      {/* Service Info */}
      <div className="bg-gray-50 rounded-xl p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-lg">
              {getServiceIcon(service.id)}
            </span>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">{service.title}</h4>
            <p className="text-sm text-gray-600">
              {getServiceDescription(service.id)}
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center text-green-600">
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>Delivery: {getDeliveryTime(service.id)}</span>
          </div>
          <div className="flex items-center text-yellow-500">
            <svg className="w-4 h-4 mr-1 fill-current" viewBox="0 0 20 20">
              <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
            </svg>
            <span>4.8 rating</span>
          </div>
        </div>
      </div>

      {/* Quantity Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-900 mb-2">
          Quantity ({unitLabel()})
        </label>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            className="w-10 h-10 rounded-lg border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 12H4"
              />
            </svg>
          </button>
          <input
            type="number"
            min={1}
            value={quantity}
            onChange={(e) => setQuantity(Math.max(1, Number(e.target.value)))}
            className="flex-1 text-center border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          />
          <button
            type="button"
            onClick={() => setQuantity(quantity + 1)}
            className="w-10 h-10 rounded-lg border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 transition-colors"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Target Details */}
      <div>
        <label className="block text-sm font-medium text-gray-900 mb-2">
          Target Details
        </label>
        <input
          value={details}
          onChange={(e) => setDetails(e.target.value)}
          className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          placeholder="e.g. @username, https://instagram.com/profile, or video URL"
        />
        <p className="text-xs text-gray-500 mt-2">
          Provide the username, profile link, or content URL where the service
          should be delivered.
        </p>
      </div>

      {/* Price Summary */}
      <div className="bg-blue-50 rounded-xl p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Base price per unit</span>
          <span className="font-medium">
            {currency === "INR" ? `₹${service.baseINR}` : `$${service.baseUSD}`}
          </span>
        </div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Quantity</span>
          <span className="font-medium">{quantity}</span>
        </div>
        <div className="border-t border-blue-200 pt-2 flex items-center justify-between">
          <span className="font-semibold text-gray-900">Total Price</span>
          <span className="text-xl font-bold text-blue-600">
            {currency === "INR"
              ? `₹${service.baseINR * quantity}`
              : `$${(service.baseUSD * quantity).toFixed(2)}`}
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <button
          className="flex-1 px-4 py-3 rounded-lg border border-gray-200 text-gray-700 font-medium hover:bg-gray-50 transition-colors"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          onClick={handleAddToCart}
          disabled={isLoading || !details.trim()}
        >
          {isLoading ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Adding...
            </>
          ) : (
            "Add to Cart"
          )}
        </button>
      </div>

      {/* Disclaimer */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <svg
              className="w-3 h-3 text-yellow-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <p className="text-xs font-medium text-yellow-900 mb-1">
              Important Notice
            </p>
            <p className="text-xs text-yellow-800 leading-relaxed">
              Delivery times may vary based on service complexity and current
              demand. Orders that violate our Terms of Service will be canceled
              and refunded.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
