<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dairy Management System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
  <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
  <script
    src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
  <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#000000"
    data-border-radius="small"></script>
</head>

<body class="bg-gray-50 min-h-screen">
  <nav class="bg-white border-b border-gray-200">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16 items-center">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <img class="h-8 w-auto" src="https://ai-public.creatie.ai/gen_page/logo_placeholder.png" alt="Logo" /><span
              class="ml-2 text-xl font-semibold text-gray-900"><a href="../index.php">DairyPro</a></span>
          </div>
          <div class="hidden md:flex md:ml-8 space-x-6">
            <a href="../cattle/cattle_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Cattle</a><a
              href="../feed/feed_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Feed</a><a
              href="../labour/labour_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Labour</a><a
              href="../production/production_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Production</a><a
              href="../sales/sales_management.php"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Sales</a>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            <span id="current-time"></span>
          </div>

        </div>
      </div>
    </div>
  </nav>
  <main class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid gap-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold">Edit Salaried Worker</h2>
          <button class="!rounded-button bg-gray-100 hover:bg-gray-200 text-gray-600 px-4 py-2 text-sm font-medium"
            onClick="window.history.back();">
            <i class="fas fa-times mr-2"></i>Cancel
          </button>
        </div>
        <form class="space-y-6">
          <div class="grid grid-cols-2 gap-6">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Employee ID</label><input type="text"
                value="EMP001" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly="" />
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Full Name</label><input type="text"
                value="John Doe" class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Position</label><select
                class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <option>Senior Manager</option>
                <option>Supervisor</option>
                <option>Team Lead</option>
                <option>Staff</option>
              </select>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Salary</label><input type="number" value="5000"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Status</label><select
                class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <option>Active</option>
                <option>On Leave</option>
                <option>Inactive</option>
              </select>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Email</label><input type="email"
                value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
          </div>
          <div class="space-y-2 mt-6">
            <label class="block text-sm font-medium text-gray-700">Additional Notes</label><textarea
              class="w-full px-3 py-2 border border-gray-300 rounded-md h-32"></textarea>
          </div>
          <div class="flex justify-end space-x-4 mt-6">
            <button class="!rounded-button bg-white border border-gray-300 text-gray-600 px-6 py-2 text-sm font-medium">
              Reset</button><button class="!rounded-button bg-custom text-white px-6 py-2 text-sm font-medium">
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  </main>
  <footer class="bg-white border-t border-gray-200 mt-8">
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          Active Sessions: 45 | Last Backup: 2 hours ago
        </div>
        <div class="flex space-x-4">
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Reports
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Settings
          </button>
          <button class="!rounded-button text-gray-600 hover:text-gray-900 text-sm">
            Support
          </button>
        </div>
      </div>
    </div>
  </footer>
  <script>
    function updateTime() {
      const now = new Date();
      document.getElementById("current-time").textContent =
        now.toLocaleString();
    }
    setInterval(updateTime, 1000);
    updateTime();
    const salesChart = echarts.init(document.getElementById("sales-chart"));
    const option = {
      animation: false,
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: "line",
          smooth: true,
          lineStyle: {
            color: "#4F46E5",
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(79, 70, 229, 0.3)",
              },
              {
                offset: 1,
                color: "rgba(79, 70, 229, 0.1)",
              },
            ]),
          },
        },
      ],
    };
    salesChart.setOption(option);
    window.addEventListener("resize", function () {
      salesChart.resize();
    });
  </script>
</body>

</html>