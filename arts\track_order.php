<?php
session_start();
include('config.php');

if (isset($_SESSION['user_id'])) {
    $userId = $_SESSION['user_id'];

    // Fetch orders for the user
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE user_id = ?");
    $stmt->execute([$userId]);
    $orders = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Orders</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <!-- Your header code -->
    </header>

    <section class="track-orders">
        <h2>Your Orders</h2>
        <?php if (!empty($orders)): ?>
            <table>
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($order['id']); ?></td>
                            <td><?php echo htmlspecialchars($order['order_date']); ?></td>
                            <td><?php echo htmlspecialchars($order['status']); ?></td>
                            <td><a href="order_details.php?id=<?php echo $order['id']; ?>" class="btn">View Details</a></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>You have no orders.</p>
        <?php endif; ?>
    </section>

    <footer>
        <!-- Your footer code -->
    </footer>
</body>
</html>
