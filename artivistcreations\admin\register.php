<?php
// Include database connection
include '../includes/db.php';

// Check if the admin is already logged in
session_start();
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Collect form data
    $name = $_POST['name'];
    $email = $_POST['email'];
    $password = $_POST['password'];

    // Validate the input
    if (!empty($name) && !empty($email) && !empty($password)) {
        // Hash the password before inserting it into the database
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Insert the admin details into the database
        $sql = "INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, 'admin')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sss', $name, $email, $hashed_password);

        if ($stmt->execute()) {
            $_SESSION['admin_registration_success'] = "Admin registration successful. You can now log in.";
            header('Location: login.php');
            exit;
        } else {
            $error = "There was an error registering the admin. Please try again.";
        }
    } else {
        $error = "All fields are required.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Registration</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>

    <div class="admin-registration">
        <h2>Create Admin Account</h2>
        <?php if (isset($error)): ?>
            <p class="error"><?php echo $error; ?></p>
        <?php endif; ?>
        <form action="register.php" method="POST">
            <div class="form-group">
                <label for="name">Name</label>
                <input type="text" name="name" id="name" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" name="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" name="password" id="password" required>
            </div>
            <button type="submit">Register</button>
        </form>
        <p>Already an admin? <a href="login.php">Log in here</a></p>
    </div>

</body>
</html>
