<?php
include 'includes/db.php';
include 'includes/header.php';

$id = $_GET['id'];
$product = $conn->query("SELECT * FROM products WHERE id=$id")->fetch_assoc();
$categories = $conn->query("SELECT * FROM categories");

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $category_id = $_POST['category_id'];
    $description = $_POST['description'];
    $price = $_POST['price'];
    $stock = $_POST['stock'];
    $status = $_POST['status'];

    // Handle Image Upload (Optional)
    if ($_FILES['image']['name']) {
        $image = $_FILES['image']['name'];
        $target = "../assets/uploads/" . basename($image);
        move_uploaded_file($_FILES['image']['tmp_name'], $target);
        $conn->query("UPDATE products SET image='$image' WHERE id=$id");
    }

    $sql = "UPDATE products SET name='$name', category_id='$category_id', description='$description', 
            price='$price', stock='$stock', status='$status' WHERE id=$id";
    
    if ($conn->query($sql)) {
        echo "<p>Product updated successfully!</p>";
    } else {
        echo "<p>Error: " . $conn->error . "</p>";
    }
}
?>

<h2>Edit Product</h2>
<form method="POST" enctype="multipart/form-data">
    Name: <input type="text" name="name" value="<?= $product['name'] ?>" required><br>
    Category:
    <select name="category_id" required>
        <?php while ($cat = $categories->fetch_assoc()): ?>
            <option value="<?= $cat['id'] ?>" <?= ($cat['id'] == $product['category_id']) ? 'selected' : '' ?>>
                <?= $cat['name'] ?>
            </option>
        <?php endwhile; ?>
    </select><br>
    Description: <textarea name="description"><?= $product['description'] ?></textarea><br>
    Price: <input type="number" name="price" value="<?= $product['price'] ?>" required><br>
    Stock: <input type="number" name="stock" value="<?= $product['stock'] ?>" required><br>
    Current Image: <img src="../assets/uploads/<?= $product['image'] ?>" width="50"><br>
    New Image: <input type="file" name="image"><br>
    Status: 
    <select name="status">
        <option value="active" <?= ($product['status'] == 'active') ? 'selected' : '' ?>>Active</option>
        <option value="inactive" <?= ($product['status'] == 'inactive') ? 'selected' : '' ?>>Inactive</option>
    </select><br>
    <input type="submit" value="Update Product">
</form>

<?php include 'includes/footer.php'; ?>
