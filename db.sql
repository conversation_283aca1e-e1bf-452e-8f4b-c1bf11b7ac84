CREATE DATABASE IF NOT EXISTS fundraising CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE fundraising;
CREATE TABLE IF NOT EXISTS donors (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(200) NOT NULL,
  email VARCHAR(200),
  city VARCHAR(100),
  amount INT NOT NULL,
  razorpay_order_id VARCHAR(100),
  razorpay_payment_id VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;