<?php
// delete_product.php

include('config.php');

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];

    // Fetch product details to get image path
    $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
    $stmt->execute([$id]);
    $product = $stmt->fetch();

    // Delete product
    $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
    $stmt->execute([$id]);

    // Remove product image file if exists
    if ($product['image']) {
        unlink("uploads/" . $product['image']);
    }

    header('Location: products.php');
    exit;
} else {
    header('Location: products.php');
    exit;
}
?>
