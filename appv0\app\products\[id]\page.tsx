import Image from "next/image";
import { Heart, ShoppingCart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

// This would typically come from your database or API
const product = {
  id: "1",
  name: "Allah Calligraphy",
  material: "Acrylic",
  price: 129.99,
  description:
    "Beautiful acrylic wall art featuring elegant Allah calligraphy. Perfect for adding a spiritual touch to any room.",
  dimensions: "24 x 36 inches",
  image: "/placeholder.svg?height=600&width=800",
  isNew: true,
  inStock: true,
  rating: 4.8,
  reviews: 24,
};

export default function ProductPage() {
  return (
    <div className="container py-12">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Product Image */}
        <div className="relative aspect-square rounded-lg overflow-hidden">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover"
            priority
          />
          {product.isNew && (
            <Badge className="absolute left-2 top-2 bg-emerald-600">New</Badge>
          )}
        </div>

        {/* Product Details */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">{product.name}</h1>
            <p className="text-gray-500 mt-2">{product.material}</p>
          </div>

          <div className="flex items-center gap-2">
            <div className="flex">
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <svg
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating)
                        ? "text-yellow-400"
                        : "text-gray-300"
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
            </div>
            <span className="text-sm text-gray-500">
              ({product.reviews} reviews)
            </span>
          </div>

          <div className="text-3xl font-bold text-emerald-600">
            ${product.price}
          </div>

          <p className="text-gray-600">{product.description}</p>

          <div className="space-y-2">
            <p className="text-sm text-gray-500">
              <span className="font-medium">Dimensions:</span>{" "}
              {product.dimensions}
            </p>
            <p className="text-sm text-gray-500">
              <span className="font-medium">Material:</span> {product.material}
            </p>
            <p className="text-sm text-gray-500">
              <span className="font-medium">Availability:</span>{" "}
              {product.inStock ? (
                <span className="text-emerald-600">In Stock</span>
              ) : (
                <span className="text-red-600">Out of Stock</span>
              )}
            </p>
          </div>

          <div className="flex gap-4">
            <Button
              size="lg"
              className="flex-1 bg-emerald-600 hover:bg-emerald-700"
              suppressHydrationWarning
            >
              <ShoppingCart className="mr-2 h-5 w-5" />
              Add to Cart
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-emerald-600 text-emerald-600 hover:bg-emerald-50"
              suppressHydrationWarning
            >
              <Heart className="mr-2 h-5 w-5" />
              Add to Favorites
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
