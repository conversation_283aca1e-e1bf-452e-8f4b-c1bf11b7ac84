<?php
define('DB_HOST', 'localhost');
define('DB_NAME', 'fundraising');
define('DB_USER', 'root');
define('DB_PASS', '');

define('RAZORPAY_KEY_ID', 'rzp_test_YOUR_KEY_ID');
define('RAZORPAY_KEY_SECRET', 'YOUR_KEY_SECRET');

define('CONTRIB_AMOUNT_PAISA', 30000);
define('GOAL_CONTRIBUTORS', 5000);
define('GOAL_AMOUNT_INR', GOAL_CONTRIBUTORS * 300);

function getPDO(){
    static $pdo = null;
    if ($pdo === null) {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4';
        $opts = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ];
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $opts);
    }
    return $pdo;
}?>