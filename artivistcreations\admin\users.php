<?php
// Include the header and database connection
include '../includes/header.php';
include '../includes/db.php';

// Check if the user is logged in and is an admin
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Fetch all users from the database
$query = "SELECT * FROM users";
$result = $conn->query($query);

// Delete user logic
if (isset($_GET['delete'])) {
    $user_id = $_GET['delete'];
    
    // Prevent deletion of admin users
    if ($user_id == $_SESSION['admin_user_id']) {
        echo "<script>alert('You cannot delete your own account');</script>";
    } else {
        $delete_query = "DELETE FROM users WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $stmt->close();

        header("Location: user.php");
        exit;
    }
}
?>

<section class="admin-users">
    <h2>Manage Users</h2>

    <?php
    if ($result->num_rows > 0) {
        echo '<table>';
        echo '<thead><tr><th>ID</th><th>Name</th><th>Email</th><th>Action</th></tr></thead>';
        echo '<tbody>';

        // Display each user in the table
        while ($row = $result->fetch_assoc()) {
            echo '<tr>';
            echo '<td>' . $row['id'] . '</td>';
            echo '<td>' . $row['name'] . '</td>';
            echo '<td>' . $row['email'] . '</td>';
            echo '<td>';
            echo '<a href="edit_user.php?id=' . $row['id'] . '" class="btn">Edit</a>';
            echo '<a href="user.php?delete=' . $row['id'] . '" class="btn" onclick="return confirm(\'Are you sure you want to delete this user?\');">Delete</a>';
            echo '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    } else {
        echo '<p>No users found.</p>';
    }
    ?>
</section>

<?php include '../includes/footer.php'; ?>
