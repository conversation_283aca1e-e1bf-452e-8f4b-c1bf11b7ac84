<?php
include 'db.php';
session_start();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$adId = $_GET['id'];

// Fetch ad details along with the username
$stmt = $pdo->prepare("SELECT ads.*, categories.name AS category_name, users.name AS username 
                        FROM ads 
                        LEFT JOIN categories ON ads.category_id = categories.id 
                        LEFT JOIN users ON ads.user_id = users.id 
                        WHERE ads.id = ?");
$stmt->execute([$adId]);
$ad = $stmt->fetch();

if (!$ad) {
    header("Location: index.php");
    exit();
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><?= htmlspecialchars($ad['title']) ?> - OLX Clone</title>
    
        <style>
            .addetailscon{
            display: flex;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            width: fit-content;
            margin-top: 10px;
            

            }
            #imagemain{
                margin-right: 10px;
            }
            #bakiitems{
                display: flex;
                flex-direction: column;
                justify-items: space-around;
                flex-wrap: nowrap;
                justify-content: space-around;
                align-items: flex-start;
            }
        </style>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="addetailscon">
        
        <div id="imagemain">
            <?php if ($ad['image']): ?>
            <img src="assets/uploads/images/<?= htmlspecialchars($ad['image']) ?>" alt="Ad Image" width="300">
            <?php endif; ?>
        </div>
        <div id="bakiitems"> 
            <h1><?= htmlspecialchars($ad['title']) ?></h1>
            <p>Category: <?= htmlspecialchars($ad['category_name']) ?></p>
            <p>Price: ₹<?= htmlspecialchars($ad['price']) ?></p>            
            <p>Posted on: <?= htmlspecialchars($ad['created_at']) ?></p>
            <p>Posted by: <?= htmlspecialchars($ad['username']) ?></p>
            <p>Description:<?= nl2br(htmlspecialchars($ad['description'])) ?></p>
        </div>       
    </div>

    <?php include 'footer.php'; ?>
</body>
</html>
