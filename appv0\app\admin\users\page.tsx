import Link from "next/link"
import { PlusCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { UsersTable } from "@/components/admin/users/users-table"

export default function UsersPage() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Users</h1>
        <Link href="/admin/users/new">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </Link>
      </div>
      <UsersTable />
    </div>
  )
}

