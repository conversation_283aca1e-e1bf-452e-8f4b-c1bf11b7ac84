import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    // Get 5 most recent completed orders with user and product details
    const recentSales = await prisma.order.findMany({
      where: {
        status: "completed",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        product: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    // Transform the data to match the expected format
    const formattedSales = recentSales.map((sale) => ({
      id: sale.id,
      total: sale.total,
      createdAt: sale.createdAt.toISOString(),
      user: {
        id: sale.user.id,
        name: sale.user.name,
        email: sale.user.email,
      },
      product: {
        name: sale.product.name,
      },
    }));

    return NextResponse.json(formattedSales);
  } catch (error) {
    console.error("Error fetching recent sales:", error);
    return NextResponse.json(
      { error: "Failed to fetch recent sales" },
      { status: 500 }
    );
  }
}
