<?php
// admin/categories.php

include('../config.php');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = $_POST['name'];
    $description = $_POST['description'];

    $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
    $stmt->execute([$name, $description]);

    header('Location: categories.php');
}

$categories = $pdo->query("SELECT * FROM categories")->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
	<link rel="stylesheet" href="../styles.css">
			<?php
		// admin/index.php

		session_start();
		if (!isset($_SESSION['logged_in'])) {
			header('Location: login.php');
			exit;
		}

		echo '<h1>Admin Panel</h1>';
		echo '<a href="categories.php">Manage Categories</a> | ';
		echo '<a href="products.php">Manage Products</a> | ';
		echo '<a href="logout.php">Logout</a>';
		?>

    <title>Manage Categories</title>
	
</head>
<body>
    <h1>Manage Categories</h1>
    <form action="categories.php" method="post">
        <input type="text" name="name" placeholder="Category Name" required>
        <textarea name="description" placeholder="Description"></textarea>
        <button type="submit">Add Category</button>
    </form>

    <h2>Existing Categories</h2>
    <ul>
        <?php foreach ($categories as $category): ?>
            <li><?php echo htmlspecialchars($category['name']); ?> - <a href="delete_category.php?id=<?php echo $category['id']; ?>">Delete</a></li>
        <?php endforeach; ?>
    </ul>
</body>
</html>
