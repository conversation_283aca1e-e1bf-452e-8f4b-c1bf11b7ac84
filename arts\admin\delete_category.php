<?php
// delete_category.php

include('config.php');

// Ensure the user is logged in
session_start();
if (!isset($_SESSION['logged_in'])) {
    header('Location: login.php');
    exit;
}

// Check if a category ID is provided
if (isset($_GET['id'])) {
    $categoryId = (int)$_GET['id'];

    // Check if the category ID is valid
    if ($categoryId > 0) {
        try {
            // Prepare and execute delete statement
            $deleteStmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
            $deleteStmt->execute([$categoryId]);

            // Optionally, you can delete associated products or handle dependencies here

            // Redirect to category management page with a success message
            header('Location: categories.php?message=Category deleted successfully');
            exit;
        } catch (PDOException $e) {
            // Handle errors
            echo "Error deleting category: " . $e->getMessage();
        }
    } else {
        echo "Invalid category ID.";
    }
} else {
    echo "No category ID provided.";
}
