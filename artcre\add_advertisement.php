<?php
session_start();
require_once 'db.php'; // Ensure this includes your PDO connection

// Retrieve form data
$title = $_POST['title'];
$category_id = $_POST['category'];
$description = $_POST['description'];
$price = $_POST['price'];
$days = $_POST['days'];
$image = $_FILES['image'];

// Calculate start and end dates
$startDate = date('Y-m-d');
$endDate = date('Y-m-d', strtotime("+$days days"));

// Handle file upload
$targetDir = 'uploads/ads/';
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0777, true); // Creates the directory if it doesn’t exist
}
$targetFile = $targetDir . basename($image['name']);
if (!move_uploaded_file($image['tmp_name'], $targetFile)) {
    die("Error: Unable to upload file.");
}


// Check if there is any active advertisement
$stmt = $pdo->query("SELECT COUNT(*) FROM advertisements WHERE is_queued = 0 AND start_date <= NOW() AND end_date >= NOW()");
$activeAdCount = $stmt->fetchColumn();

// Set `is_queued` based on the presence of active advertisements
$isQueued = ($activeAdCount > 0) ? 1 : 0;

// Insert the new advertisement into the database
// Use 'category' instead of 'category_id' if that’s the correct column name in your table
$insertStmt = $pdo->prepare("INSERT INTO advertisements (title, category, description, price, image, start_date, end_date, is_queued) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
$insertStmt->execute([$title, $category_id, $description, $price, basename($image['name']), $startDate, $endDate, $isQueued]);

// Redirect to advertisement management page
header("Location: advertise_section_management.php");
exit;
?>
