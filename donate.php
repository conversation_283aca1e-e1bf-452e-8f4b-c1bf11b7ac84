<?php
require 'config.php';
?>
<!doctype html>
<html>
<head><meta charset="utf-8"><title>Contribute ₹300</title></head>
<body>
<h2>Contribute ₹300</h2>
<form id="donorForm">
  <input type="text" id="name" placeholder="Full Name" required><br>
  <input type="email" id="email" placeholder="Email"><br>
  <input type="text" id="city" placeholder="City"><br>
  <button type="submit">Pay ₹300</button>
</form>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
document.getElementById('donorForm').addEventListener('submit', async function(e){
  e.preventDefault();
  const name=document.getElementById('name').value;
  const email=document.getElementById('email').value;
  const city=document.getElementById('city').value;
  const resp=await fetch('create_order.php',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({name,email,city})});
  const data=await resp.json();
  if(!data.success){alert('Error creating order');return;}
  const order=data.order;
  const options={
    "key":"<?= RAZORPAY_KEY_ID ?>",
    "amount":order.amount,
    "currency":"INR",
    "name":"5000 Dost — Wall of Kindness",
    "description":"Contribution ₹300",
    "order_id":order.id,
    "handler":function(response){
      fetch('payment_success.php',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({
        name,email,city,
        razorpay_order_id:response.razorpay_order_id,
        razorpay_payment_id:response.razorpay_payment_id,
        razorpay_signature:response.razorpay_signature
      })}).then(r=>r.json()).then(res=>{
        if(res.success){alert('Thank you!');window.location.href='hall.php';}
        else{alert('Verification failed');}
      });
    }
  };
  const rzp=new Razorpay(options);
  rzp.open();
});
</script>
</body>
</html>