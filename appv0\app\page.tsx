"use client";

import Link from "next/link";
import Image from "next/image";
import { Heart, ShoppingCart } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { useCart } from "@/context/CartContext";
import { useAuth } from "@/context/auth-context";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";

export default function Home() {
  const router = useRouter();
  const { user, toggleFavorite } = useAuth();
  const { items, addItem } = useCart();

  const handleFavorite = async (productId: string) => {
    if (!user) {
      toast.error("Please sign in to add favorites");
      router.push("/signin");
      return;
    }

    try {
      await toggleFavorite(productId);
      toast.success(
        user.favorites.includes(productId)
          ? "Removed from favorites"
          : "Added to favorites"
      );
    } catch (error) {
      toast.error("Failed to update favorites");
    }
  };

  const handleAddToCart = (product: any) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.image,
    });
    toast.success("Added to cart");
  };

  return (
    <>
      {/* Hero Section */}
      <section className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-900/70 to-emerald-700/50 z-10" />
        <div className="relative h-[500px] md:h-[600px] w-full">
          <Image
            src="/placeholder.svg?height=600&width=1200"
            alt="Islamic Wall Art Collection"
            fill
            className="object-cover"
            priority
          />
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 absolute inset-0 z-20 flex flex-col items-start justify-center gap-4 text-white">
          <h1 className="max-w-2xl text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
            Elevate Your Space with Islamic Art
          </h1>
          <p className="max-w-md text-lg text-gray-100">
            Discover our handcrafted collection of Islamic wall art and home
            decor made from premium materials.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <Link href="/shop">
              <Button
                className="bg-emerald-600 hover:bg-emerald-700"
                suppressHydrationWarning
              >
                Shop Now
              </Button>
            </Link>
            <Link href="/collections">
              <Button
                className="border-white text-white hover:bg-white hover:text-emerald-800"
                suppressHydrationWarning
              >
                Explore Collections
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Materials Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center mb-8">
            Crafted with Premium Materials
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div className="p-6 bg-white rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-2">Acrylic</h3>
              <p className="text-gray-600">
                Modern, sleek designs with vibrant colors
              </p>
            </div>
            <div className="p-6 bg-white rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-2">Metal</h3>
              <p className="text-gray-600">
                Elegant, durable pieces with timeless appeal
              </p>
            </div>
            <div className="p-6 bg-white rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-2">PVC</h3>
              <p className="text-gray-600">
                Lightweight, versatile designs for any space
              </p>
            </div>
            <div className="p-6 bg-white rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-2">Wood</h3>
              <p className="text-gray-600">
                Natural, warm aesthetic with traditional charm
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Sale Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <h2 className="text-3xl font-bold">Sale</h2>
              <Badge className="text-lg px-4 py-1 bg-destructive text-destructive-foreground">
                Up to 50% Off
              </Badge>
            </div>
            <Link
              href="/shop?category=sale"
              className="text-emerald-600 hover:underline"
            >
              View All
            </Link>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {saleProducts.map((product) => (
              <Card
                key={product.id}
                className="group cursor-pointer relative"
                onClick={() => router.push(`/products/${product.id}`)}
              >
                <div className="relative aspect-square">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button
                      className="bg-white/20 hover:bg-white/30 text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFavorite(product.id);
                      }}
                    >
                      <Heart
                        className={`h-5 w-5 ${
                          user?.favorites.includes(product.id)
                            ? "fill-red-500 text-red-500"
                            : ""
                        }`}
                      />
                    </Button>
                    <Button
                      className="bg-white/20 hover:bg-white/30 text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddToCart(product);
                      }}
                    >
                      <ShoppingCart className="h-5 w-5" />
                    </Button>
                  </div>
                  <Badge className="absolute top-2 left-2 bg-destructive text-destructive-foreground">
                    {product.discount}% Off
                  </Badge>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-medium">{product.name}</h3>
                  <p className="text-sm text-gray-500">{product.material}</p>
                  <div className="flex flex-col gap-1 mt-2">
                    <div className="flex items-center gap-2">
                      <span className="text-red-600 font-semibold">
                        ${product.salePrice}
                      </span>
                      <span className="text-sm text-gray-500 line-through">
                        ${product.originalPrice}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddToCart(product);
                      }}
                      suppressHydrationWarning
                    >
                      {items.some((item) => item.id === product.id)
                        ? "Added to Cart"
                        : "Add to Cart"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 container">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Featured Products</h2>
          <Link href="/shop" className="text-emerald-600 hover:underline">
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredProducts.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover transition-transform group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
                <Button
                  className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFavorite(product.id);
                  }}
                  suppressHydrationWarning
                >
                  <Heart
                    className={`h-4 w-4 ${
                      user?.favorites.includes(product.id)
                        ? "fill-red-500 text-red-500"
                        : ""
                    }`}
                  />
                </Button>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Latest Products */}
      <section className="py-16 container bg-gray-50">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Latest Products</h2>
          <Link href="/shop" className="text-emerald-600 hover:underline">
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {latestProducts.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover transition-transform group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
                <Button
                  size="icon"
                  variant="secondary"
                  className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFavorite(product.id);
                  }}
                >
                  <Heart
                    className={`h-4 w-4 ${
                      user?.favorites.includes(product.id)
                        ? "fill-red-500 text-red-500"
                        : ""
                    }`}
                  />
                </Button>
                {product.isNew && (
                  <Badge className="absolute left-2 top-2 bg-emerald-600">
                    New
                  </Badge>
                )}
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* New Arrivals */}
      <section className="py-16 container">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">New Arrivals</h2>
          <Link
            href="/shop?category=new"
            className="text-emerald-600 hover:underline"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {newArrivals.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFavorite(product.id);
                    }}
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        user?.favorites.includes(product.id)
                          ? "fill-red-500 text-red-500"
                          : ""
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                </div>
                {product.isNew && (
                  <Badge className="absolute top-2 left-2 bg-emerald-600">
                    New
                  </Badge>
                )}
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Best Sellers */}
      <section className="py-16 container bg-gray-50">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Best Sellers</h2>
          <Link
            href="/shop?category=bestsellers"
            className="text-emerald-600 hover:underline"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {bestSellers.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFavorite(product.id);
                    }}
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        user?.favorites.includes(product.id)
                          ? "fill-red-500 text-red-500"
                          : ""
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                </div>
                <Badge className="absolute top-2 left-2 bg-amber-600">
                  Best Seller
                </Badge>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Home Decor */}
      <section className="py-16 container">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Home Decor</h2>
          <Link
            href="/shop?category=home-decor"
            className="text-emerald-600 hover:underline"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {homeDecor.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFavorite(product.id);
                    }}
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        user?.favorites.includes(product.id)
                          ? "fill-red-500 text-red-500"
                          : ""
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Wall Art */}
      <section className="py-16 container bg-gray-50">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Wall Art</h2>
          <Link
            href="/shop?category=wall-art"
            className="text-emerald-600 hover:underline"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {wallArt.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFavorite(product.id);
                    }}
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        user?.favorites.includes(product.id)
                          ? "fill-red-500 text-red-500"
                          : ""
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Islamic Art */}
      <section className="py-16 container">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Islamic Art</h2>
          <Link
            href="/shop?category=islamic"
            className="text-emerald-600 hover:underline"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {islamicArt.map((product) => (
            <Card
              key={product.id}
              className="group cursor-pointer"
              onClick={() => router.push(`/products/${product.id}`)}
            >
              <div className="relative aspect-square">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFavorite(product.id);
                    }}
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        user?.favorites.includes(product.id)
                          ? "fill-red-500 text-red-500"
                          : ""
                      }`}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/20 hover:bg-white/30 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.material}</p>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-emerald-600 font-semibold">
                    ${product.price}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    suppressHydrationWarning
                  >
                    {items.some((item) => item.id === product.id)
                      ? "Added to Cart"
                      : "Add to Cart"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Customer Reviews */}
      <section className="py-16 container">
        <h2 className="text-3xl font-bold text-center mb-12">
          What Our Customers Say
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {reviews.map((review) => (
            <Card key={review.id} className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="relative w-12 h-12 rounded-full overflow-hidden">
                  <Image
                    src={review.avatar}
                    alt={review.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold">{review.name}</h3>
                  <div className="flex">
                    {Array(5)
                      .fill(0)
                      .map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${
                            i < review.rating
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 italic">{review.text}</p>
            </Card>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 container bg-gray-50">
        <h2 className="text-3xl font-bold text-center mb-12">
          Frequently Asked Questions
        </h2>
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq) => (
              <AccordionItem key={faq.id} value={faq.id}>
                <AccordionTrigger
                  className="text-left"
                  suppressHydrationWarning
                >
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-16 container bg-emerald-700 text-white">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">Join Our Newsletter</h2>
          <p className="mb-6">
            Subscribe to receive updates on new products, special offers, and
            Islamic art inspiration.
          </p>
          <div className="flex flex-col sm:flex-row gap-2 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Your email address"
              className="flex-1 px-4 py-2 rounded-md text-gray-900"
              suppressHydrationWarning
            />
            <Button
              className="bg-white text-emerald-700 hover:bg-gray-100"
              suppressHydrationWarning
            >
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </>
  );
}

// Sample data
const featuredProducts = [
  {
    id: "1",
    name: "Allah Calligraphy",
    material: "Acrylic",
    price: 129.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "2",
    name: "Ayatul Kursi Frame",
    material: "Wood",
    price: 149.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "3",
    name: "Geometric Pattern",
    material: "Metal",
    price: 199.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fc0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "4",
    name: "Kaaba Silhouette",
    material: "PVC",
    price: 89.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fc1?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
];

const latestProducts = [
  {
    id: "5",
    name: "Bismillah Script",
    material: "Metal",
    price: 159.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fc2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
  {
    id: "6",
    name: "Mosque Silhouette",
    material: "Wood",
    price: 119.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fc3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
  {
    id: "7",
    name: "99 Names of Allah",
    material: "Acrylic",
    price: 249.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fc4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
  {
    id: "8",
    name: "Islamic Geometric Art",
    material: "PVC",
    price: 99.99,
    image:
      "https://images.unsplash.com/photo-1618005182384-a83a8bd57fc5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
];

const reviews = [
  {
    id: "1",
    name: "Sarah Ahmed",
    rating: 5,
    avatar: "/placeholder.svg?height=100&width=100",
    text: "The quality of the acrylic wall art exceeded my expectations. The colors are vibrant and it looks stunning in my living room.",
  },
  {
    id: "2",
    name: "Mohammed Ali",
    rating: 5,
    avatar: "/placeholder.svg?height=100&width=100",
    text: "I purchased the wooden Ayatul Kursi frame and it's absolutely beautiful. The craftsmanship is excellent and delivery was prompt.",
  },
  {
    id: "3",
    name: "Fatima Khan",
    rating: 4,
    avatar: "/placeholder.svg?height=100&width=100",
    text: "Love my new metal wall art! It adds the perfect touch of elegance to my prayer room. Would definitely recommend Artivist Creations.",
  },
];

const faqs = [
  {
    id: "1",
    question: "What materials do you use for your Islamic wall art?",
    answer:
      "We use premium quality acrylic, metal, PVC, and wood for our Islamic wall art and home decor pieces. Each material is carefully selected to ensure durability and aesthetic appeal.",
  },
  {
    id: "2",
    question: "How do I care for my Islamic wall art?",
    answer:
      "For acrylic and PVC pieces, use a soft, dry cloth to gently remove dust. For wooden items, use a slightly damp cloth and avoid harsh chemicals. Metal pieces can be cleaned with a dry microfiber cloth.",
  },
  {
    id: "3",
    question: "Do you offer custom designs?",
    answer:
      "Yes, we offer custom designs for all our products. Please contact our customer service team with your requirements, and we'll be happy to create a personalized piece for you.",
  },
  {
    id: "4",
    question: "What is your shipping policy?",
    answer:
      "We offer worldwide shipping. Standard shipping takes 5-7 business days within the UAE and 10-15 business days internationally. Express shipping options are also available at checkout.",
  },
  {
    id: "5",
    question: "Can I return or exchange my purchase?",
    answer:
      "Yes, we offer a 30-day return policy for all our products. Items must be in their original condition and packaging. Custom orders are non-refundable unless there's a manufacturing defect.",
  },
];

const newArrivals = [
  {
    id: "9",
    name: "Bismillah Wall Art",
    material: "Acrylic",
    price: 89.99,
    image:
      "https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
  {
    id: "10",
    name: "Islamic Pattern Mirror",
    material: "Metal",
    price: 129.99,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
  {
    id: "11",
    name: "Mashallah Sign",
    material: "Wood",
    price: 59.99,
    image:
      "https://images.unsplash.com/photo-1542640244-7e672d6cef4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
  {
    id: "12",
    name: "Arabic Calligraphy Frame",
    material: "Acrylic",
    price: 79.99,
    image:
      "https://images.unsplash.com/photo-1564507592333-c60657eea523?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    isNew: true,
  },
];

const bestSellers = [
  {
    id: "13",
    name: "Allah Name Art",
    material: "Metal",
    price: 149.99,
    image:
      "https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "14",
    name: "Ayatul Kursi Frame",
    material: "Wood",
    price: 129.99,
    image:
      "https://images.unsplash.com/photo-1542640244-7e672d6cef4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "15",
    name: "Islamic Wall Clock",
    material: "Acrylic",
    price: 69.99,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "16",
    name: "Prayer Mat Stand",
    material: "Wood",
    price: 49.99,
    image:
      "https://images.unsplash.com/photo-1584551246679-0daf3d275d0f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
];

const homeDecor = [
  {
    id: "17",
    name: "Islamic Pattern Cushion",
    material: "Fabric",
    price: 39.99,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "18",
    name: "Arabic Calligraphy Lamp",
    material: "Glass",
    price: 79.99,
    image:
      "https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "19",
    name: "Islamic Table Runner",
    material: "Fabric",
    price: 29.99,
    image:
      "https://images.unsplash.com/photo-1542640244-7e672d6cef4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "20",
    name: "Geometric Pattern Vase",
    material: "Ceramic",
    price: 49.99,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
];

const wallArt = [
  {
    id: "21",
    name: "Subhanallah Canvas",
    material: "Canvas",
    price: 99.99,
    image:
      "https://images.unsplash.com/photo-1564507592333-c60657eea523?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "22",
    name: "Islamic Pattern Wall Panel",
    material: "Wood",
    price: 199.99,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "23",
    name: "Arabic Quote Print",
    material: "Paper",
    price: 29.99,
    image:
      "https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "24",
    name: "Geometric Wall Art",
    material: "Metal",
    price: 149.99,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
];

const islamicArt = [
  {
    id: "25",
    name: "99 Names of Allah",
    material: "Acrylic",
    price: 249.99,
    image:
      "https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "26",
    name: "Kaaba Silhouette",
    material: "Metal",
    price: 179.99,
    image:
      "https://images.unsplash.com/photo-1537444532052-2afbf769b76c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "27",
    name: "Islamic Calligraphy Set",
    material: "Wood",
    price: 199.99,
    image:
      "https://images.unsplash.com/photo-1564507592333-c60657eea523?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "28",
    name: "Mosque Art",
    material: "Acrylic",
    price: 159.99,
    image:
      "https://images.unsplash.com/photo-1584551246679-0daf3d275d0f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
];

const saleProducts = [
  {
    id: "29",
    name: "Islamic Wall Art Set",
    material: "Acrylic",
    originalPrice: 299.99,
    salePrice: 149.99,
    discount: 50,
    image:
      "https://images.unsplash.com/photo-1564507592333-c60657eea523?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "30",
    name: "Arabic Calligraphy Mirror",
    material: "Metal",
    originalPrice: 199.99,
    salePrice: 139.99,
    discount: 30,
    image:
      "https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "31",
    name: "Prayer Room Decor Set",
    material: "Wood",
    originalPrice: 249.99,
    salePrice: 174.99,
    discount: 30,
    image:
      "https://images.unsplash.com/photo-1584551246679-0daf3d275d0f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "32",
    name: "Islamic Pattern Wall Panel",
    material: "Metal",
    originalPrice: 179.99,
    salePrice: 89.99,
    discount: 50,
    image:
      "https://images.unsplash.com/photo-1565843708714-52ecf69ab81f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  },
];
