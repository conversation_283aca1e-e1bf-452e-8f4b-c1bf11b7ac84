import Image from "next/image";
import Link from "next/link";

export default function CollectionsPage() {
  return (
    <main className="container py-16">
      <h1 className="text-4xl font-bold mb-8">Collections</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {collections.map((collection) => (
          <Link
            key={collection.id}
            href={`/collections/${collection.id}`}
            className="group relative overflow-hidden rounded-lg"
          >
            <div className="relative aspect-[3/4] w-full">
              <Image
                src={collection.image}
                alt={collection.name}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/50 transition-opacity group-hover:opacity-70" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-white mb-2">
                    {collection.name}
                  </h2>
                  <p className="text-white/90">{collection.itemCount} items</p>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </main>
  );
}

const collections = [
  {
    id: "calligraphy",
    name: "Islamic Calligraphy",
    itemCount: 24,
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: "geometric",
    name: "Geometric Patterns",
    itemCount: 18,
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: "modern",
    name: "Modern Islamic Art",
    itemCount: 16,
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: "architecture",
    name: "Islamic Architecture",
    itemCount: 12,
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: "arabic",
    name: "Arabic Typography",
    itemCount: 20,
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: "minimalist",
    name: "Minimalist Designs",
    itemCount: 15,
    image: "/placeholder.svg?height=600&width=400",
  },
];
