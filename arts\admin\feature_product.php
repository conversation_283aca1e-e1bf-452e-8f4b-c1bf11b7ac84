<?php
include('config.php');

// Ensure the user is logged in
session_start();
if (!isset($_SESSION['logged_in'])) {
    header('Location: login.php');
    exit;
}

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];

    // Update the product to be featured
    $stmt = $pdo->prepare("UPDATE products SET is_featured = 1 WHERE id = ?");
    $stmt->execute([$id]);

    // Redirect back to the products page
    header('Location: products.php');
    exit;
} else {
    // Redirect if no ID is provided
    header('Location: products.php');
    exit;
}
